# 📚 Multiple Image Upload & PDF Generation Demo

A complete implementation demonstrating how to upload multiple images at once and automatically generate a combined PDF document.

## 🎯 Key Features

- **📁 Multiple Image Upload**: Select multiple images in one go (hold Ctrl/Cmd)
- **📄 Automatic PDF Generation**: Combine uploaded images into a single PDF
- **🎨 Professional Layout**: Smart image scaling and positioning
- **💾 Database Storage**: SQLite database for document management
- **📥 Download PDFs**: Download generated PDF documents

## 🚀 Quick Start

### Prerequisites
- Python 3.8+
- pip (Python package manager)

### Installation & Setup

1. **Install Dependencies**
```bash
cd backend
pip install -r requirements.txt
```

2. **Start the Demo Server**
```bash
python multiple_image_demo.py
```

3. **Open Demo Interface**
- Go to: http://localhost:8000/docs
- Interactive API documentation with testing interface

## 🧪 How to Test Multiple Image Upload

### Using the API Documentation (Recommended)

1. **Open**: http://localhost:8000/docs
2. **Find**: `POST /api/v1/documents/upload-multiple`
3. **Click**: "Try it out"
4. **Select Multiple Images**:
   - Click the file picker
   - **Hold Ctrl (Windows) or Cmd (Mac)**
   - **Click multiple image files** to select them all at once
   - Click "Open"
5. **Configure Options**:
   - Set `generate_pdf` = `true`
   - Set `pdf_name` = `"My_Combined_PDF"`
6. **Click**: "Execute"
7. **Result**: All images uploaded + PDF automatically generated

## 📋 Available Endpoints

### 🔥 Main Feature
- **POST** `/api/v1/documents/upload-multiple` - Upload multiple images & generate PDF

### 📊 Management
- **GET** `/api/v1/documents` - List all uploaded documents
- **POST** `/api/v1/documents/images-to-pdf` - Convert existing images to PDF
- **GET** `/download/{filename}` - Download generated PDFs

## 🎨 Technical Implementation

### Backend Stack
- **FastAPI** - Modern Python web framework
- **SQLAlchemy** - Database ORM with SQLite
- **ReportLab** - Professional PDF generation
- **Pillow (PIL)** - Image processing
- **Uvicorn** - ASGI server

## 📁 Project Structure

```
├── backend/
│   ├── multiple_image_demo.py    # 🔥 Main demo server
│   ├── requirements.txt          # Python dependencies
│   └── uploads/                  # Generated files storage
├── frontend/                     # React components (optional)
└── README.md                     # This file
```

## 🏆 Success Criteria

You've successfully tested the demo when:
- ✅ Multiple images selected in one file picker dialog
- ✅ All images uploaded simultaneously
- ✅ PDF automatically generated from uploaded images
- ✅ PDF downloads successfully
- ✅ Professional PDF layout with proper image scaling

---

**🎉 Enjoy testing the multiple image upload and PDF generation demo!**
