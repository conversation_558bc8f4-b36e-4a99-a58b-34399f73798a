import React, { useCallback, useState } from 'react';
import { useDropzone } from 'react-dropzone';
import { useDocuments } from 'hooks/useDocuments';

interface UploadZoneProps {
  onMultipleUpload?: (files: File[], generatePdf?: boolean, pdfName?: string) => Promise<void>;
}

export const UploadZone: React.FC<UploadZoneProps> = ({ onMultipleUpload }) => {
  const { uploadDocument } = useDocuments();
  const [showPdfOptions, setShowPdfOptions] = useState(false);
  const [pdfName, setPdfName] = useState('');
  const [generatePdf, setGeneratePdf] = useState(false);
  const [pendingFiles, setPendingFiles] = useState<File[]>([]);

  const handleUpload = useCallback(async (files: File[]) => {
    if (files.length === 1) {
      // Single file upload
      await uploadDocument(files[0]);
    } else if (files.length > 1) {
      // Multiple files - check if they're images and show PDF option
      const imageFiles = files.filter(file => file.type.startsWith('image/'));

      if (imageFiles.length > 1) {
        setPendingFiles(files);
        setShowPdfOptions(true);
        setPdfName(`combined_${Date.now()}`);
      } else {
        // Upload multiple files individually
        if (onMultipleUpload) {
          await onMultipleUpload(files);
        } else {
          for (const file of files) {
            await uploadDocument(file);
          }
        }
      }
    }
  }, [uploadDocument, onMultipleUpload]);

  const onDrop = useCallback(async (acceptedFiles: File[]) => {
    await handleUpload(acceptedFiles);
  }, [handleUpload]);

  const handlePdfGeneration = useCallback(async () => {
    if (onMultipleUpload && pendingFiles.length > 0) {
      await onMultipleUpload(pendingFiles, generatePdf, pdfName);
    } else {
      // Fallback to individual uploads
      for (const file of pendingFiles) {
        await uploadDocument(file);
      }
    }

    setShowPdfOptions(false);
    setPendingFiles([]);
    setGeneratePdf(false);
    setPdfName('');
  }, [onMultipleUpload, pendingFiles, generatePdf, pdfName, uploadDocument]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'application/pdf': ['.pdf'],
      'image/*': ['.png', '.jpg', '.jpeg', '.tiff']
    },
    maxSize: 10 * 1024 * 1024, // 10MB
    multiple: true
  });

  return (
    <>
      <div
        {...getRootProps()}
        className={`
          p-10 border-2 border-dashed rounded-lg text-center cursor-pointer
          ${isDragActive ? 'border-primary-500 bg-primary-50' : 'border-gray-300'}
        `}
      >
        <input {...getInputProps()} />
        {isDragActive ? (
          <p className="text-primary-600">Drop the files here...</p>
        ) : (
          <div>
            <p className="text-gray-600">Drag and drop files here, or click to select files</p>
            <p className="text-sm text-gray-500 mt-2">
              Supported formats: PDF, PNG, JPG, TIFF (max 10MB each)
            </p>
            <p className="text-sm text-blue-500 mt-1">
              Select multiple images to combine into a PDF
            </p>
          </div>
        )}
      </div>

      {/* PDF Generation Options Modal */}
      {showPdfOptions && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white p-6 rounded-lg max-w-md w-full mx-4">
            <h3 className="text-lg font-semibold mb-4">
              Combine {pendingFiles.length} images into PDF?
            </h3>

            <div className="space-y-4">
              <div>
                <label className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    checked={generatePdf}
                    onChange={(e) => setGeneratePdf(e.target.checked)}
                    className="rounded"
                  />
                  <span>Generate combined PDF</span>
                </label>
              </div>

              {generatePdf && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    PDF Name
                  </label>
                  <input
                    type="text"
                    value={pdfName}
                    onChange={(e) => setPdfName(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="Enter PDF name"
                  />
                </div>
              )}

              <div className="flex space-x-3 pt-4">
                <button
                  onClick={handlePdfGeneration}
                  className="flex-1 bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  {generatePdf ? 'Upload & Generate PDF' : 'Upload Files'}
                </button>
                <button
                  onClick={() => {
                    setShowPdfOptions(false);
                    setPendingFiles([]);
                    setGeneratePdf(false);
                  }}
                  className="flex-1 bg-gray-300 text-gray-700 py-2 px-4 rounded-md hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-500"
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
};
