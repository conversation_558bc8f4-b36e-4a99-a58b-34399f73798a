import axios from 'axios';

const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8001';

const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Response interceptor to handle errors
api.interceptors.response.use(
  (response) => response,
  (error) => {
    console.error('API Error:', error);
    return Promise.reject(error);
  }
);

// Document API endpoints
export const documentAPI = {
  uploadMultiple: (formData: FormData) => {
    return api.post('/api/v1/documents/upload-multiple', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  },

  getDocuments: () => {
    return api.get('/api/v1/documents');
  },

  downloadDocument: (filename: string) => {
    return `${API_BASE_URL}/download/${filename}`;
  },
};

export default api;
