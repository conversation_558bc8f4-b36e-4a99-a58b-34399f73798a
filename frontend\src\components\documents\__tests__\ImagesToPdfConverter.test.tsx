import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { ImagesToPdfConverter } from '../ImagesToPdfConverter';
import { useDocuments } from '../../../hooks/useDocuments';

// Mock the useDocuments hook
jest.mock('../../../hooks/useDocuments');
const mockUseDocuments = useDocuments as jest.MockedFunction<typeof useDocuments>;

describe('ImagesToPdfConverter', () => {
  const mockConvertImagesToPdf = jest.fn();
  const mockOnConversionComplete = jest.fn();

  const mockImageDocuments = [
    { id: 1, name: 'image1.png', file_path: '/path/to/image1.png', mime_type: 'image/png' },
    { id: 2, name: 'image2.jpg', file_path: '/path/to/image2.jpg', mime_type: 'image/jpeg' },
    { id: 3, name: 'document.pdf', file_path: '/path/to/document.pdf', mime_type: 'application/pdf' }
  ];

  beforeEach(() => {
    jest.clearAllMocks();
    mockUseDocuments.mockReturnValue({
      documents: mockImageDocuments,
      loading: false,
      error: null,
      fetchDocuments: jest.fn(),
      uploadDocument: jest.fn(),
      uploadMultipleDocuments: jest.fn(),
      convertImagesToPdf: mockConvertImagesToPdf,
      deleteDocument: jest.fn()
    });
  });

  it('renders converter with image documents', () => {
    render(<ImagesToPdfConverter />);
    
    expect(screen.getByText('Convert Images to PDF')).toBeInTheDocument();
    expect(screen.getByText('Select Images (0 selected)')).toBeInTheDocument();
    expect(screen.getByText('image1.png')).toBeInTheDocument();
    expect(screen.getByText('image2.jpg')).toBeInTheDocument();
    // PDF document should not be shown
    expect(screen.queryByText('document.pdf')).not.toBeInTheDocument();
  });

  it('shows no images message when no image documents exist', () => {
    mockUseDocuments.mockReturnValue({
      documents: [{ id: 3, name: 'document.pdf', file_path: '/path/to/document.pdf', mime_type: 'application/pdf' }],
      loading: false,
      error: null,
      fetchDocuments: jest.fn(),
      uploadDocument: jest.fn(),
      uploadMultipleDocuments: jest.fn(),
      convertImagesToPdf: mockConvertImagesToPdf,
      deleteDocument: jest.fn()
    });

    render(<ImagesToPdfConverter />);
    
    expect(screen.getByText('No image documents found. Upload some images first.')).toBeInTheDocument();
  });

  it('handles image selection', () => {
    render(<ImagesToPdfConverter />);
    
    const image1 = screen.getByText('image1.png').closest('div');
    fireEvent.click(image1!);
    
    expect(screen.getByText('Select Images (1 selected)')).toBeInTheDocument();
    
    const image2 = screen.getByText('image2.jpg').closest('div');
    fireEvent.click(image2!);
    
    expect(screen.getByText('Select Images (2 selected)')).toBeInTheDocument();
  });

  it('handles select all functionality', () => {
    render(<ImagesToPdfConverter />);
    
    const selectAllButton = screen.getByText('Select All');
    fireEvent.click(selectAllButton);
    
    expect(screen.getByText('Select Images (2 selected)')).toBeInTheDocument();
    expect(screen.getByText('Deselect All')).toBeInTheDocument();
    
    fireEvent.click(selectAllButton);
    expect(screen.getByText('Select Images (0 selected)')).toBeInTheDocument();
  });

  it('shows PDF configuration when images are selected', () => {
    render(<ImagesToPdfConverter />);
    
    const image1 = screen.getByText('image1.png').closest('div');
    fireEvent.click(image1!);
    
    expect(screen.getByText('PDF Name')).toBeInTheDocument();
    expect(screen.getByText('Page Orientation')).toBeInTheDocument();
    expect(screen.getByText('Image Fit')).toBeInTheDocument();
    expect(screen.getByText('Convert 1 Images to PDF')).toBeInTheDocument();
  });

  it('auto-generates PDF name when images are selected', () => {
    render(<ImagesToPdfConverter />);
    
    const image1 = screen.getByText('image1.png').closest('div');
    fireEvent.click(image1!);
    
    const pdfNameInput = screen.getByDisplayValue(/combined_images_/);
    expect(pdfNameInput).toBeInTheDocument();
  });

  it('handles successful PDF conversion', async () => {
    mockConvertImagesToPdf.mockResolvedValue({
      pdf_document_id: 4,
      pdf_name: 'my_pdf',
      status: 'processing',
      message: 'PDF generation started'
    });

    render(<ImagesToPdfConverter onConversionComplete={mockOnConversionComplete} />);
    
    // Select images
    const image1 = screen.getByText('image1.png').closest('div');
    fireEvent.click(image1!);
    
    // Set PDF name
    const pdfNameInput = screen.getByPlaceholderText('Enter PDF name');
    fireEvent.change(pdfNameInput, { target: { value: 'my_pdf' } });
    
    // Convert to PDF
    const convertButton = screen.getByText('Convert 1 Images to PDF');
    fireEvent.click(convertButton);
    
    await waitFor(() => {
      expect(mockConvertImagesToPdf).toHaveBeenCalledWith([1], 'my_pdf', 'portrait', 'contain');
    });
    
    await waitFor(() => {
      expect(screen.getByText(/Successfully started PDF generation for 1 images/)).toBeInTheDocument();
    });
    
    expect(mockOnConversionComplete).toHaveBeenCalled();
  });

  it('handles conversion error', async () => {
    mockConvertImagesToPdf.mockRejectedValue(new Error('Conversion failed'));

    render(<ImagesToPdfConverter />);
    
    // Select images
    const image1 = screen.getByText('image1.png').closest('div');
    fireEvent.click(image1!);
    
    // Set PDF name
    const pdfNameInput = screen.getByPlaceholderText('Enter PDF name');
    fireEvent.change(pdfNameInput, { target: { value: 'my_pdf' } });
    
    // Convert to PDF
    const convertButton = screen.getByText('Convert 1 Images to PDF');
    fireEvent.click(convertButton);
    
    await waitFor(() => {
      expect(screen.getByText('Conversion failed: Conversion failed')).toBeInTheDocument();
    });
  });

  it('validates PDF name before conversion', () => {
    render(<ImagesToPdfConverter />);
    
    // Select images
    const image1 = screen.getByText('image1.png').closest('div');
    fireEvent.click(image1!);
    
    // Clear PDF name
    const pdfNameInput = screen.getByPlaceholderText('Enter PDF name');
    fireEvent.change(pdfNameInput, { target: { value: '' } });
    
    // Try to convert
    const convertButton = screen.getByText('Convert 1 Images to PDF');
    fireEvent.click(convertButton);
    
    expect(screen.getByText('Please select images and enter a PDF name')).toBeInTheDocument();
  });

  it('handles page orientation change', () => {
    render(<ImagesToPdfConverter />);
    
    // Select images
    const image1 = screen.getByText('image1.png').closest('div');
    fireEvent.click(image1!);
    
    // Change orientation
    const orientationSelect = screen.getByDisplayValue('Portrait');
    fireEvent.change(orientationSelect, { target: { value: 'landscape' } });
    
    expect(orientationSelect).toHaveValue('landscape');
  });

  it('handles image fit mode change', () => {
    render(<ImagesToPdfConverter />);
    
    // Select images
    const image1 = screen.getByText('image1.png').closest('div');
    fireEvent.click(image1!);
    
    // Change fit mode
    const fitSelect = screen.getByDisplayValue('Contain (fit within page)');
    fireEvent.change(fitSelect, { target: { value: 'cover' } });
    
    expect(fitSelect).toHaveValue('cover');
  });

  it('shows loading state during conversion', async () => {
    mockConvertImagesToPdf.mockImplementation(() => 
      new Promise(resolve => setTimeout(resolve, 100))
    );

    render(<ImagesToPdfConverter />);
    
    // Select images and set name
    const image1 = screen.getByText('image1.png').closest('div');
    fireEvent.click(image1!);
    
    const pdfNameInput = screen.getByPlaceholderText('Enter PDF name');
    fireEvent.change(pdfNameInput, { target: { value: 'my_pdf' } });
    
    // Start conversion
    const convertButton = screen.getByText('Convert 1 Images to PDF');
    fireEvent.click(convertButton);
    
    await waitFor(() => {
      expect(screen.getByText('Converting...')).toBeInTheDocument();
    });
  });
});
