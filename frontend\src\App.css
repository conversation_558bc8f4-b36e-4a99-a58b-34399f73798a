/* Custom styles for the Multiple Image to PDF Converter */

.upload-zone {
  transition: all 0.3s ease;
}

.upload-zone:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.upload-zone.drag-active {
  border-color: #3b82f6;
  background-color: #eff6ff;
}

.file-preview {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.progress-bar {
  transition: width 0.3s ease;
}

.success-animation {
  animation: bounce 0.6s ease-in-out;
}

@keyframes bounce {
  0%, 20%, 60%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  80% {
    transform: translateY(-5px);
  }
}
