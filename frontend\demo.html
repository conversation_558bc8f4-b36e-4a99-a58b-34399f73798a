<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Multiple Image to PDF Converter - Frontend Demo</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .upload-zone {
            transition: all 0.3s ease;
        }
        .upload-zone:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }
        .upload-zone.drag-active {
            border-color: #3b82f6;
            background-color: #eff6ff;
        }
        .file-preview {
            animation: fadeIn 0.3s ease-in-out;
        }
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        .success-animation {
            animation: bounce 0.6s ease-in-out;
        }
        @keyframes bounce {
            0%, 20%, 60%, 100% { transform: translateY(0); }
            40% { transform: translateY(-10px); }
            80% { transform: translateY(-5px); }
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Header -->
    <header class="bg-white shadow-sm border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <div class="flex items-center">
                    <div class="flex items-center space-x-3">
                        <div class="w-10 h-10 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                            </svg>
                        </div>
                        <div>
                            <span class="text-xl font-bold text-gray-900">Image to PDF</span>
                            <div class="text-xs text-gray-500">Converter</div>
                        </div>
                    </div>
                </div>
                <nav class="flex space-x-1">
                    <button class="px-4 py-2 rounded-lg text-sm font-medium bg-blue-100 text-blue-700">
                        🏠 Home
                    </button>
                    <button class="px-4 py-2 rounded-lg text-sm font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-100">
                        📄 Documents
                    </button>
                </nav>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
            <!-- Hero Section -->
            <div class="text-center mb-16">
                <div class="mb-8">
                    <div class="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl mb-6">
                        <svg class="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                        </svg>
                    </div>
                </div>
                
                <h1 class="text-5xl font-bold text-gray-900 sm:text-6xl md:text-7xl mb-6">
                    Multiple Images to
                    <span class="text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-purple-600"> PDF</span>
                </h1>
                
                <p class="mt-6 max-w-3xl mx-auto text-xl text-gray-600 leading-relaxed">
                    Convert multiple images into a single, professional PDF document instantly. 
                    No registration required, completely free, and your files are processed securely.
                </p>
                
                <div class="mt-8 flex flex-col sm:flex-row gap-4 justify-center">
                    <a href="#converter" class="inline-flex items-center px-8 py-4 border border-transparent text-lg font-medium rounded-xl text-white bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-1">
                        🚀 Start Converting
                    </a>
                    <button class="inline-flex items-center px-8 py-4 border-2 border-gray-300 text-lg font-medium rounded-xl text-gray-700 bg-white hover:bg-gray-50 transition-all duration-200 shadow-md hover:shadow-lg">
                        📄 View Documents
                    </button>
                </div>
            </div>

            <!-- Features Section -->
            <div class="mb-16">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-5xl mx-auto">
                    <div class="text-center p-6 bg-white rounded-2xl shadow-md hover:shadow-lg transition-shadow">
                        <div class="w-16 h-16 bg-blue-100 rounded-xl flex items-center justify-center mx-auto mb-4">
                            <span class="text-2xl">📚</span>
                        </div>
                        <h3 class="text-xl font-semibold text-gray-900 mb-3">Multiple Images</h3>
                        <p class="text-gray-600">
                            Select multiple images at once using Ctrl/Cmd + click. Supports PNG, JPG, and JPEG formats.
                        </p>
                    </div>
                    
                    <div class="text-center p-6 bg-white rounded-2xl shadow-md hover:shadow-lg transition-shadow">
                        <div class="w-16 h-16 bg-green-100 rounded-xl flex items-center justify-center mx-auto mb-4">
                            <span class="text-2xl">⚡</span>
                        </div>
                        <h3 class="text-xl font-semibold text-gray-900 mb-3">Instant Processing</h3>
                        <p class="text-gray-600">
                            Fast in-memory processing. Your images are converted to PDF in seconds, not minutes.
                        </p>
                    </div>
                    
                    <div class="text-center p-6 bg-white rounded-2xl shadow-md hover:shadow-lg transition-shadow">
                        <div class="w-16 h-16 bg-purple-100 rounded-xl flex items-center justify-center mx-auto mb-4">
                            <span class="text-2xl">🔒</span>
                        </div>
                        <h3 class="text-xl font-semibold text-gray-900 mb-3">Privacy First</h3>
                        <p class="text-gray-600">
                            Images are processed in memory and not stored. Only the final PDF is saved temporarily.
                        </p>
                    </div>
                </div>
            </div>

            <!-- Converter Section -->
            <div id="converter" class="max-w-4xl mx-auto">
                <div class="text-center mb-8">
                    <h2 class="text-3xl font-bold text-gray-900 mb-4">Convert Your Images Now</h2>
                    <p class="text-lg text-gray-600">Upload multiple images and get a professional PDF in seconds</p>
                </div>
                
                <!-- Upload Component -->
                <div class="max-w-4xl mx-auto p-6 bg-white rounded-2xl shadow-lg">
                    <!-- Upload Zone -->
                    <div class="upload-zone border-2 border-dashed border-gray-300 rounded-xl p-8 text-center cursor-pointer transition-all duration-300 hover:border-blue-400 hover:bg-gray-50">
                        <div class="space-y-4">
                            <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto">
                                <svg class="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                                </svg>
                            </div>
                            
                            <div>
                                <p class="text-lg text-gray-700 font-medium mb-2">
                                    Drop multiple images here, or click to select
                                </p>
                                <p class="text-sm text-gray-500">
                                    💡 Hold <kbd class="px-2 py-1 bg-gray-200 rounded text-xs">Ctrl</kbd> (Windows) or
                                    <kbd class="px-2 py-1 bg-gray-200 rounded text-xs">Cmd</kbd> (Mac) to select multiple files
                                </p>
                                <p class="text-xs text-gray-400 mt-2">
                                    Supports PNG, JPG, JPEG • Max 10MB per file
                                </p>
                            </div>
                        </div>
                    </div>

                    <!-- Demo Message -->
                    <div class="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <svg class="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
                                </svg>
                            </div>
                            <div class="ml-3">
                                <h3 class="text-sm font-medium text-blue-800">
                                    📱 Frontend Demo
                                </h3>
                                <div class="mt-2 text-sm text-blue-700">
                                    <p>This is a static demo of the React frontend. To use the full functionality:</p>
                                    <ul class="mt-2 list-disc list-inside">
                                        <li>Install Node.js and run <code class="bg-blue-100 px-1 rounded">npm install</code></li>
                                        <li>Start the development server with <code class="bg-blue-100 px-1 rounded">npm start</code></li>
                                        <li>Ensure the backend is running on <code class="bg-blue-100 px-1 rounded">http://localhost:8001</code></li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- PDF Name Input -->
                    <div class="mt-6">
                        <label for="pdfName" class="block text-sm font-medium text-gray-700 mb-2">PDF Name</label>
                        <input type="text" id="pdfName" value="Combined_Images_PDF" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent" placeholder="Enter PDF name">
                    </div>

                    <!-- Convert Button -->
                    <div class="mt-6">
                        <button class="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-4 rounded-lg transition-colors duration-200 text-lg">
                            🚀 Convert Images to PDF
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="bg-white border-t border-gray-200 py-8">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <p class="text-gray-600">
                🎉 <strong>Frontend Development Complete!</strong> Modern React TypeScript interface ready for the Multiple Image to PDF Converter.
            </p>
            <p class="text-sm text-gray-500 mt-2">
                Built with React, TypeScript, Tailwind CSS, and React Dropzone
            </p>
        </div>
    </footer>
</body>
</html>
