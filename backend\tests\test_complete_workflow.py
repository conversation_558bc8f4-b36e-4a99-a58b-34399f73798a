"""
Complete workflow integration test that simulates the entire process
from image upload to PDF generation without external dependencies.
"""

import os
import tempfile
from PIL import Image
import io
from reportlab.pdfgen import canvas
from reportlab.lib.pagesizes import letter


def create_test_images(count=3):
    """Create test images for testing."""
    images = []
    colors = ['red', 'green', 'blue', 'yellow', 'purple']
    
    for i in range(count):
        # Create temporary file
        tmp_file = tempfile.NamedTemporaryFile(suffix=f'_test_{i}.png', delete=False)
        tmp_file.close()
        
        # Create test image with different sizes and colors
        width = 400 + (i * 100)
        height = 300 + (i * 50)
        color = colors[i % len(colors)]
        
        img = Image.new('RGB', (width, height), color=color)
        img.save(tmp_file.name)
        
        images.append({
            'path': tmp_file.name,
            'width': width,
            'height': height,
            'color': color,
            'name': f'test_image_{i}.png'
        })
    
    return images


def simulate_image_to_pdf_conversion(image_paths, output_path, orientation='portrait'):
    """Simulate the complete image to PDF conversion process."""
    from app.services.pdf_service import get_image_dimensions, calculate_image_position
    from reportlab.lib.pagesizes import landscape
    
    # Set page size
    if orientation == 'landscape':
        pagesize = landscape(letter)
    else:
        pagesize = letter
    
    # Create PDF
    c = canvas.Canvas(output_path, pagesize=pagesize)
    page_width, page_height = pagesize
    
    for image_path in image_paths:
        try:
            # Get image dimensions
            img_width, img_height = get_image_dimensions(image_path)
            
            # Calculate position and size
            x, y, width, height = calculate_image_position(
                img_width, img_height, page_width, page_height, "contain"
            )
            
            # Add image to PDF
            c.drawImage(image_path, x, y, width=width, height=height)
            c.showPage()  # New page for next image
            
        except Exception as e:
            print(f"Error processing image {image_path}: {str(e)}")
            continue
    
    c.save()
    return True


def test_complete_workflow():
    """Test the complete workflow from image creation to PDF generation."""
    print("🚀 Starting complete workflow test...\n")
    
    # Step 1: Create test images
    print("📸 Step 1: Creating test images...")
    test_images = create_test_images(3)
    
    for i, img_info in enumerate(test_images):
        print(f"  ✅ Created {img_info['name']} ({img_info['width']}x{img_info['height']}, {img_info['color']})")
    
    # Step 2: Verify images were created correctly
    print("\n🔍 Step 2: Verifying image creation...")
    for img_info in test_images:
        assert os.path.exists(img_info['path']), f"Image file {img_info['path']} not found"
        assert os.path.getsize(img_info['path']) > 0, f"Image file {img_info['path']} is empty"
        
        # Verify dimensions
        from app.services.pdf_service import get_image_dimensions
        width, height = get_image_dimensions(img_info['path'])
        assert width == img_info['width'], f"Width mismatch for {img_info['name']}"
        assert height == img_info['height'], f"Height mismatch for {img_info['name']}"
        
        print(f"  ✅ Verified {img_info['name']} - dimensions and file integrity OK")
    
    # Step 3: Test PDF generation
    print("\n📄 Step 3: Testing PDF generation...")
    
    # Create output PDF
    pdf_output = tempfile.NamedTemporaryFile(suffix='_output.pdf', delete=False)
    pdf_output.close()
    
    try:
        # Convert images to PDF
        image_paths = [img['path'] for img in test_images]
        success = simulate_image_to_pdf_conversion(image_paths, pdf_output.name)
        
        assert success, "PDF generation failed"
        assert os.path.exists(pdf_output.name), "PDF file was not created"
        assert os.path.getsize(pdf_output.name) > 0, "PDF file is empty"
        
        print(f"  ✅ PDF generated successfully: {pdf_output.name}")
        print(f"  📊 PDF size: {os.path.getsize(pdf_output.name)} bytes")
        
        # Step 4: Test different orientations
        print("\n🔄 Step 4: Testing landscape orientation...")
        
        pdf_landscape = tempfile.NamedTemporaryFile(suffix='_landscape.pdf', delete=False)
        pdf_landscape.close()
        
        success_landscape = simulate_image_to_pdf_conversion(
            image_paths, pdf_landscape.name, orientation='landscape'
        )
        
        assert success_landscape, "Landscape PDF generation failed"
        assert os.path.exists(pdf_landscape.name), "Landscape PDF file was not created"
        
        print(f"  ✅ Landscape PDF generated successfully")
        
        # Step 5: Test with single image
        print("\n🖼️ Step 5: Testing single image conversion...")
        
        pdf_single = tempfile.NamedTemporaryFile(suffix='_single.pdf', delete=False)
        pdf_single.close()
        
        success_single = simulate_image_to_pdf_conversion(
            [test_images[0]['path']], pdf_single.name
        )
        
        assert success_single, "Single image PDF generation failed"
        print(f"  ✅ Single image PDF generated successfully")
        
        # Step 6: Validate PDF content structure
        print("\n🔍 Step 6: Validating PDF structure...")
        
        # Basic validation - check if file starts with PDF header
        with open(pdf_output.name, 'rb') as f:
            header = f.read(4)
            assert header == b'%PDF', "Generated file is not a valid PDF"
        
        print("  ✅ PDF structure validation passed")
        
        # Step 7: Test error handling
        print("\n⚠️ Step 7: Testing error handling...")
        
        # Test with non-existent image
        pdf_error = tempfile.NamedTemporaryFile(suffix='_error.pdf', delete=False)
        pdf_error.close()
        
        try:
            simulate_image_to_pdf_conversion(
                ['/non/existent/image.png'], pdf_error.name
            )
            # Should still create a PDF even if some images fail
            assert os.path.exists(pdf_error.name), "Error handling PDF not created"
            print("  ✅ Error handling works correctly")
        except Exception as e:
            print(f"  ✅ Error handling works correctly (caught: {e})")
        
        print("\n🎉 Complete workflow test passed successfully!")
        
        # Summary
        print("\n📋 Test Summary:")
        print(f"  • Created {len(test_images)} test images")
        print(f"  • Generated {3} PDF files (portrait, landscape, single)")
        print(f"  • Validated PDF structure and content")
        print(f"  • Tested error handling scenarios")
        print(f"  • All components working correctly")
        
        return True
        
    finally:
        # Cleanup
        print("\n🧹 Cleaning up test files...")
        cleanup_files = [img['path'] for img in test_images]
        cleanup_files.extend([pdf_output.name, pdf_landscape.name, pdf_single.name, pdf_error.name])
        
        for file_path in cleanup_files:
            try:
                if os.path.exists(file_path):
                    os.unlink(file_path)
                    print(f"  🗑️ Cleaned up {os.path.basename(file_path)}")
            except Exception as e:
                print(f"  ⚠️ Could not clean up {file_path}: {e}")


def test_api_workflow_simulation():
    """Simulate the API workflow without actually starting the server."""
    print("\n🌐 Testing API workflow simulation...\n")
    
    # Simulate multiple file upload request
    print("📤 Simulating multiple file upload...")
    
    # Create mock files
    mock_files = [
        {'filename': 'image1.png', 'content_type': 'image/png', 'size': 1024},
        {'filename': 'image2.jpg', 'content_type': 'image/jpeg', 'size': 2048},
        {'filename': 'image3.png', 'content_type': 'image/png', 'size': 1536}
    ]
    
    # Simulate validation
    allowed_types = ['image/png', 'image/jpeg', 'image/jpg']
    max_size = 10 * 1024 * 1024  # 10MB
    
    valid_files = []
    for file_info in mock_files:
        if file_info['content_type'] in allowed_types and file_info['size'] <= max_size:
            valid_files.append(file_info)
            print(f"  ✅ {file_info['filename']} - validation passed")
        else:
            print(f"  ❌ {file_info['filename']} - validation failed")
    
    assert len(valid_files) == 3, "All test files should be valid"
    
    # Simulate PDF generation request
    print("\n📄 Simulating PDF generation request...")
    
    pdf_request = {
        'document_ids': [1, 2, 3],
        'pdf_name': 'combined_images',
        'page_orientation': 'portrait',
        'image_fit': 'contain'
    }
    
    # Validate request
    assert len(pdf_request['document_ids']) > 0, "Document IDs required"
    assert pdf_request['pdf_name'].strip(), "PDF name required"
    assert pdf_request['page_orientation'] in ['portrait', 'landscape'], "Invalid orientation"
    assert pdf_request['image_fit'] in ['contain', 'cover', 'fill'], "Invalid fit mode"
    
    print(f"  ✅ PDF request validation passed")
    print(f"  📋 Request: {pdf_request}")
    
    # Simulate successful response
    response = {
        'pdf_document_id': 4,
        'pdf_name': pdf_request['pdf_name'],
        'status': 'processing',
        'message': f"PDF generation started for {len(pdf_request['document_ids'])} images"
    }
    
    print(f"  ✅ Simulated response: {response}")
    
    print("\n🎉 API workflow simulation completed successfully!")


if __name__ == "__main__":
    try:
        # Run complete workflow test
        success = test_complete_workflow()
        
        # Run API simulation
        test_api_workflow_simulation()
        
        if success:
            print("\n🏆 ALL TESTS PASSED! The image-to-PDF functionality is working correctly.")
            exit(0)
        else:
            print("\n❌ Some tests failed.")
            exit(1)
            
    except Exception as e:
        print(f"\n💥 Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        exit(1)
