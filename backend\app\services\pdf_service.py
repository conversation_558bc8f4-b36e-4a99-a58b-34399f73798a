from reportlab.pdfgen import canvas
from reportlab.lib.pagesizes import letter, A4, landscape
from reportlab.lib.utils import ImageReader
from PIL import Image
import os
import uuid
from typing import List, Optional

from app.database import SessionLocal
from app.models.document import Document, DocumentStatus
from app.celery_app import celery_app


@celery_app.task
def generate_pdf_from_text(document_id: int, content: str):
    """Generate PDF from text content."""
    db = SessionLocal()
    try:
        document = db.query(Document).filter(Document.id == document_id).first()
        if not document:
            return {"error": "Document not found"}

        # Update status to processing
        db.query(Document).filter(Document.id == document_id).update({"status": "processing"})
        db.commit()

        # Get file path for processing
        file_path = db.query(Document.file_path).filter(Document.id == document_id).scalar()

        # Create PDF using ReportLab
        pdf_path = f"{os.path.splitext(file_path)[0]}_generated.pdf"
        c = canvas.Canvas(pdf_path, pagesize=letter)

        # Add text to PDF
        width, height = letter
        y_position = height - 50

        for line in content.split('\n'):
            if y_position < 50:  # Start new page if needed
                c.showPage()
                y_position = height - 50
            c.drawString(50, y_position, line[:80])  # Limit line length
            y_position -= 20

        c.save()

        # Update document with new PDF path
        db.query(Document).filter(Document.id == document_id).update({
            "file_path": pdf_path,
            "status": "completed"
        })
        db.commit()

        return {"success": True, "document_id": document_id, "pdf_path": pdf_path}
    except Exception as e:
        db.query(Document).filter(Document.id == document_id).update({"status": "failed"})
        db.commit()
        return {"error": str(e)}
    finally:
        db.close()


def get_image_dimensions(image_path: str) -> tuple[int, int]:
    """Get image dimensions."""
    with Image.open(image_path) as img:
        return img.size


def calculate_image_position(img_width: int, img_height: int, page_width: float, page_height: float,
                           fit_mode: str = "contain") -> tuple[float, float, float, float]:
    """Calculate image position and size to fit on page."""
    margin = 50
    available_width = page_width - 2 * margin
    available_height = page_height - 2 * margin

    if fit_mode == "contain":
        # Scale image to fit within page while maintaining aspect ratio
        scale_x = available_width / img_width
        scale_y = available_height / img_height
        scale = min(scale_x, scale_y)

        new_width = img_width * scale
        new_height = img_height * scale

        # Center the image
        x = margin + (available_width - new_width) / 2
        y = margin + (available_height - new_height) / 2

        return x, y, new_width, new_height

    elif fit_mode == "fill":
        # Fill entire page (may crop image)
        return margin, margin, available_width, available_height

    else:  # cover
        # Scale to cover entire page while maintaining aspect ratio
        scale_x = available_width / img_width
        scale_y = available_height / img_height
        scale = max(scale_x, scale_y)

        new_width = img_width * scale
        new_height = img_height * scale

        # Center the image (may be cropped)
        x = margin + (available_width - new_width) / 2
        y = margin + (available_height - new_height) / 2

        return x, y, new_width, new_height


@celery_app.task
def generate_pdf_from_images(document_ids: List[int], pdf_name: str,
                           page_orientation: str = "portrait",
                           image_fit: str = "contain",
                           user_id: Optional[int] = None) -> dict:
    """Generate PDF from multiple images."""
    db = SessionLocal()
    try:
        # Get all documents
        documents = db.query(Document).filter(
            Document.id.in_(document_ids),
            Document.user_id == user_id
        ).all()

        if not documents:
            return {"error": "No documents found"}

        # Validate all documents are images
        image_extensions = {'.png', '.jpg', '.jpeg', '.tiff', '.bmp'}
        for doc in documents:
            # Get file path as string
            file_path = db.query(Document.file_path).filter(Document.id == doc.id).scalar()
            file_ext = os.path.splitext(file_path)[1].lower()
            if file_ext not in image_extensions:
                doc_name = db.query(Document.name).filter(Document.id == doc.id).scalar()
                return {"error": f"Document {doc_name} is not an image"}

        # Set page size
        if page_orientation == "landscape":
            pagesize = landscape(letter)
        else:
            pagesize = letter

        # Generate unique filename for PDF
        pdf_filename = f"{uuid.uuid4()}_{pdf_name}.pdf"
        pdf_path = os.path.join("uploads", pdf_filename)

        # Create PDF
        c = canvas.Canvas(pdf_path, pagesize=pagesize)
        page_width, page_height = pagesize

        for doc in documents:
            try:
                # Get file path as string
                file_path = db.query(Document.file_path).filter(Document.id == doc.id).scalar()

                # Get image dimensions
                img_width, img_height = get_image_dimensions(file_path)

                # Calculate position and size
                x, y, width, height = calculate_image_position(
                    img_width, img_height, page_width, page_height, image_fit
                )

                # Add image to PDF
                c.drawImage(file_path, x, y, width=width, height=height)
                c.showPage()  # New page for next image

            except Exception as e:
                doc_name = db.query(Document.name).filter(Document.id == doc.id).scalar()
                print(f"Error processing image {doc_name}: {str(e)}")
                continue

        c.save()

        # Create new document record for the PDF
        pdf_document = Document(
            name=f"{pdf_name}.pdf",
            file_path=pdf_path,
            mime_type="application/pdf",
            user_id=user_id,
            status="completed"
        )

        db.add(pdf_document)
        db.commit()
        db.refresh(pdf_document)

        return {
            "success": True,
            "pdf_document_id": pdf_document.id,
            "pdf_path": pdf_path,
            "message": f"PDF generated successfully with {len(documents)} images"
        }

    except Exception as e:
        return {"error": str(e)}
    finally:
        db.close()
