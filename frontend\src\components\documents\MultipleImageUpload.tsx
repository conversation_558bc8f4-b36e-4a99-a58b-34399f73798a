import React, { useState, useCallback } from 'react';
import { useDropzone } from 'react-dropzone';
import axios from 'axios';

interface UploadedFile {
  file: File;
  preview: string;
  id: string;
}

interface ProcessResult {
  processed_count: number;
  pdf_result?: {
    pdf_name: string;
    pdf_size: number;
    pages: number;
    download_url: string;
    status: string;
  };
  message: string;
}

const MultipleImageUpload: React.FC = () => {
  const [files, setFiles] = useState<UploadedFile[]>([]);
  const [pdfName, setPdfName] = useState('My_Combined_PDF');
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [result, setResult] = useState<ProcessResult | null>(null);
  const [error, setError] = useState<string | null>(null);

  const onDrop = useCallback((acceptedFiles: File[]) => {
    const newFiles = acceptedFiles.map(file => ({
      file,
      preview: URL.createObjectURL(file),
      id: Math.random().toString(36).substr(2, 9)
    }));
    
    setFiles(prev => [...prev, ...newFiles]);
    setResult(null);
    setError(null);
  }, []);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'image/*': ['.png', '.jpg', '.jpeg']
    },
    multiple: true
  });

  const removeFile = (id: string) => {
    setFiles(prev => {
      const updated = prev.filter(f => f.id !== id);
      // Revoke object URL to prevent memory leaks
      const fileToRemove = prev.find(f => f.id === id);
      if (fileToRemove) {
        URL.revokeObjectURL(fileToRemove.preview);
      }
      return updated;
    });
  };

  const clearAll = () => {
    files.forEach(f => URL.revokeObjectURL(f.preview));
    setFiles([]);
    setResult(null);
    setError(null);
  };

  const handleUpload = async () => {
    if (files.length === 0) {
      setError('Please select at least one image');
      return;
    }

    if (!pdfName.trim()) {
      setError('Please enter a PDF name');
      return;
    }

    setIsUploading(true);
    setUploadProgress(0);
    setError(null);
    setResult(null);

    try {
      const formData = new FormData();
      
      // Add all files
      files.forEach(({ file }) => {
        formData.append('files', file);
      });
      
      // Add PDF generation parameters
      formData.append('generate_pdf', 'true');
      formData.append('pdf_name', pdfName.trim());

      const response = await axios.post<ProcessResult>(
        'http://localhost:8001/api/v1/documents/upload-multiple',
        formData,
        {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
          onUploadProgress: (progressEvent) => {
            if (progressEvent.total) {
              const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total);
              setUploadProgress(progress);
            }
          },
        }
      );

      setResult(response.data);
      
      // Clear files after successful upload
      setTimeout(() => {
        clearAll();
      }, 2000);

    } catch (err: any) {
      console.error('Upload error:', err);
      setError(
        err.response?.data?.detail || 
        err.message || 
        'Upload failed. Please try again.'
      );
    } finally {
      setIsUploading(false);
      setUploadProgress(0);
    }
  };

  const downloadPdf = (downloadUrl: string, filename: string) => {
    const link = document.createElement('a');
    link.href = `http://localhost:8001${downloadUrl}`;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  return (
    <div className="max-w-4xl mx-auto p-6 bg-white rounded-2xl shadow-lg">
      {/* Upload Zone */}
      <div
        {...getRootProps()}
        className={`upload-zone border-2 border-dashed rounded-xl p-8 text-center cursor-pointer transition-all duration-300 ${
          isDragActive
            ? 'border-blue-500 bg-blue-50 drag-active'
            : 'border-gray-300 hover:border-blue-400 hover:bg-gray-50'
        }`}
      >
        <input {...getInputProps()} />
        <div className="space-y-4">
          <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto">
            <svg className="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
            </svg>
          </div>
          
          {isDragActive ? (
            <p className="text-lg text-blue-600 font-medium">Drop your images here!</p>
          ) : (
            <div>
              <p className="text-lg text-gray-700 font-medium mb-2">
                Drop multiple images here, or click to select
              </p>
              <p className="text-sm text-gray-500">
                💡 Hold <kbd className="px-2 py-1 bg-gray-200 rounded text-xs">Ctrl</kbd> (Windows) or{' '}
                <kbd className="px-2 py-1 bg-gray-200 rounded text-xs">Cmd</kbd> (Mac) to select multiple files
              </p>
              <p className="text-xs text-gray-400 mt-2">
                Supports PNG, JPG, JPEG • Max 10MB per file
              </p>
            </div>
          )}
        </div>
      </div>

      {/* Selected Files */}
      {files.length > 0 && (
        <div className="mt-6">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-semibold text-gray-900">
              Selected Images ({files.length})
            </h3>
            <button
              onClick={clearAll}
              className="text-sm text-red-600 hover:text-red-800 font-medium"
            >
              Clear All
            </button>
          </div>
          
          <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4 max-h-64 overflow-y-auto">
            {files.map(({ file, preview, id }) => (
              <div key={id} className="file-preview relative group">
                <div className="aspect-square bg-gray-100 rounded-lg overflow-hidden">
                  <img
                    src={preview}
                    alt={file.name}
                    className="w-full h-full object-cover"
                  />
                </div>
                <button
                  onClick={() => removeFile(id)}
                  className="absolute -top-2 -right-2 w-6 h-6 bg-red-500 text-white rounded-full flex items-center justify-center text-xs opacity-0 group-hover:opacity-100 transition-opacity"
                >
                  ×
                </button>
                <p className="text-xs text-gray-600 mt-1 truncate" title={file.name}>
                  {file.name}
                </p>
                <p className="text-xs text-gray-400">
                  {(file.size / 1024 / 1024).toFixed(1)} MB
                </p>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* PDF Name Input */}
      {files.length > 0 && (
        <div className="mt-6">
          <label htmlFor="pdfName" className="block text-sm font-medium text-gray-700 mb-2">
            PDF Name
          </label>
          <input
            type="text"
            id="pdfName"
            value={pdfName}
            onChange={(e) => setPdfName(e.target.value)}
            className="input-field"
            placeholder="Enter PDF name"
            disabled={isUploading}
          />
        </div>
      )}

      {/* Upload Button */}
      {files.length > 0 && (
        <div className="mt-6">
          <button
            onClick={handleUpload}
            disabled={isUploading || files.length === 0}
            className="w-full btn-primary disabled:opacity-50 disabled:cursor-not-allowed relative overflow-hidden"
          >
            {isUploading ? (
              <div className="flex items-center justify-center">
                <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Converting... {uploadProgress}%
              </div>
            ) : (
              `🚀 Convert ${files.length} Image${files.length > 1 ? 's' : ''} to PDF`
            )}
            
            {isUploading && (
              <div 
                className="absolute bottom-0 left-0 h-1 bg-blue-300 progress-bar"
                style={{ width: `${uploadProgress}%` }}
              />
            )}
          </button>
        </div>
      )}

      {/* Results */}
      {result && (
        <div className="mt-6 p-4 bg-green-50 border border-green-200 rounded-lg success-animation">
          <div className="flex items-start">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3 flex-1">
              <h3 className="text-sm font-medium text-green-800">
                ✅ PDF Generated Successfully!
              </h3>
              <div className="mt-2 text-sm text-green-700">
                <p>{result.message}</p>
                {result.pdf_result && (
                  <div className="mt-3 space-y-1">
                    <p><strong>PDF Name:</strong> {result.pdf_result.pdf_name}</p>
                    <p><strong>Size:</strong> {(result.pdf_result.pdf_size / 1024 / 1024).toFixed(2)} MB</p>
                    <p><strong>Pages:</strong> {result.pdf_result.pages}</p>
                    <button
                      onClick={() => result.pdf_result && downloadPdf(result.pdf_result.download_url, result.pdf_result.pdf_name)}
                      className="mt-2 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700"
                    >
                      📥 Download PDF
                    </button>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Error */}
      {error && (
        <div className="mt-6 p-4 bg-red-50 border border-red-200 rounded-lg">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">
                ❌ Upload Failed
              </h3>
              <div className="mt-2 text-sm text-red-700">
                <p>{error}</p>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default MultipleImageUpload;
