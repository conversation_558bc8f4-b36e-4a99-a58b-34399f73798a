"""
Quick test to check basic image processing limits
"""

import requests
from PIL import Image
import tempfile
import os
import time

def create_test_images(count):
    """Create test images."""
    images = []
    colors = ['red', 'green', 'blue', 'yellow', 'purple']
    
    for i in range(count):
        tmp_file = tempfile.NamedTemporaryFile(suffix=f'_test_{i}.png', delete=False)
        tmp_file.close()
        
        color = colors[i % len(colors)]
        img = Image.new('RGB', (400, 300), color=color)
        img.save(tmp_file.name)
        
        images.append({
            'path': tmp_file.name,
            'name': f'test_{i+1}.png'
        })
    
    return images

def test_image_count(count):
    """Test processing a specific number of images."""
    print(f"\n🧪 Testing {count} images")
    
    base_url = "http://localhost:8001"
    
    # Create test images
    start_time = time.time()
    test_images = create_test_images(count)
    creation_time = time.time() - start_time
    
    # Calculate total size
    total_size = sum(os.path.getsize(img['path']) for img in test_images)
    print(f"   📁 Total size: {total_size / 1024 / 1024:.1f} MB")
    print(f"   ⏱️ Creation time: {creation_time:.1f}s")
    
    try:
        # Prepare files
        files = []
        for img in test_images:
            with open(img['path'], 'rb') as f:
                files.append(('files', (img['name'], f.read(), 'image/png')))
        
        data = {
            'generate_pdf': 'true',
            'pdf_name': f'Quick_Test_{count}_Images'
        }
        
        # Send request
        upload_start = time.time()
        response = requests.post(f"{base_url}/api/v1/documents/upload-multiple", 
                               files=files, data=data, timeout=120)
        upload_time = time.time() - upload_start
        
        if response.status_code == 200:
            result = response.json()
            pdf_info = result.get('pdf_result', {})
            
            print(f"   ✅ SUCCESS!")
            print(f"   📄 Processed: {result['processed_count']} images")
            print(f"   📄 PDF size: {pdf_info.get('pdf_size', 0) / 1024 / 1024:.1f} MB")
            print(f"   ⏱️ Upload time: {upload_time:.1f}s")
            print(f"   ⏱️ Total time: {creation_time + upload_time:.1f}s")
            return True
        else:
            print(f"   ❌ FAILED: {response.status_code}")
            print(f"   Error: {response.text[:100]}...")
            return False
            
    except Exception as e:
        print(f"   ❌ ERROR: {e}")
        return False
    
    finally:
        # Cleanup
        for img in test_images:
            try:
                os.unlink(img['path'])
            except:
                pass

def quick_limit_test():
    """Run quick tests with increasing image counts."""
    print("🚀 QUICK LIMIT TEST")
    print("=" * 30)
    
    # Test increasing counts until failure
    test_counts = [5, 10, 20, 30, 50, 75, 100]
    max_successful = 0
    
    for count in test_counts:
        success = test_image_count(count)
        if success:
            max_successful = count
        else:
            print(f"\n❌ Failed at {count} images")
            break
    
    print(f"\n🏆 RESULTS:")
    print(f"✅ Maximum successful: {max_successful} images")
    
    # Test some edge cases
    if max_successful >= 50:
        print(f"\n🔍 Testing larger counts...")
        for count in [150, 200, 300]:
            if count > max_successful:
                success = test_image_count(count)
                if success:
                    max_successful = count
                else:
                    break
    
    return max_successful

if __name__ == "__main__":
    max_images = quick_limit_test()
    print(f"\n🎯 FINAL RESULT: Can process up to {max_images} images successfully")
