import pytest
import os
import tempfile
from unittest.mock import Mock, patch, MagicMock
from PIL import Image
import io

from app.services.pdf_service import (
    get_image_dimensions,
    calculate_image_position,
    generate_pdf_from_images,
    generate_pdf_from_text
)
from app.models.document import Document


class TestPdfService:
    
    def test_get_image_dimensions(self):
        """Test getting image dimensions."""
        # Create a test image
        with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as tmp_file:
            img = Image.new('RGB', (800, 600), color='red')
            img.save(tmp_file.name)
            
            width, height = get_image_dimensions(tmp_file.name)
            assert width == 800
            assert height == 600
            
            # Clean up
            os.unlink(tmp_file.name)
    
    def test_calculate_image_position_contain(self):
        """Test image position calculation with contain mode."""
        # Image smaller than page
        x, y, width, height = calculate_image_position(400, 300, 612, 792, "contain")
        
        # Should be centered and maintain aspect ratio
        assert width == 400
        assert height == 300
        assert x > 50  # Should have margin
        assert y > 50  # Should have margin
    
    def test_calculate_image_position_fill(self):
        """Test image position calculation with fill mode."""
        x, y, width, height = calculate_image_position(400, 300, 612, 792, "fill")
        
        # Should fill available space
        assert x == 50  # Margin
        assert y == 50  # Margin
        assert width == 512  # Page width - 2*margin
        assert height == 692  # Page height - 2*margin
    
    def test_calculate_image_position_cover(self):
        """Test image position calculation with cover mode."""
        x, y, width, height = calculate_image_position(400, 300, 612, 792, "cover")
        
        # Should scale to cover entire page
        assert width > 512 or height > 692  # At least one dimension should exceed available space
    
    @patch('app.services.pdf_service.SessionLocal')
    @patch('app.services.pdf_service.canvas.Canvas')
    def test_generate_pdf_from_images_success(self, mock_canvas, mock_session):
        """Test successful PDF generation from images."""
        # Mock database session
        mock_db = Mock()
        mock_session.return_value = mock_db
        
        # Mock documents
        mock_doc1 = Mock()
        mock_doc1.id = 1
        mock_doc1.name = "image1.png"
        mock_doc1.file_path = "/path/to/image1.png"
        
        mock_doc2 = Mock()
        mock_doc2.id = 2
        mock_doc2.name = "image2.jpg"
        mock_doc2.file_path = "/path/to/image2.jpg"
        
        mock_db.query.return_value.filter.return_value.all.return_value = [mock_doc1, mock_doc2]
        mock_db.query.return_value.filter.return_value.scalar.return_value = "/path/to/image1.png"
        
        # Mock canvas
        mock_canvas_instance = Mock()
        mock_canvas.return_value = mock_canvas_instance
        
        # Mock image dimensions
        with patch('app.services.pdf_service.get_image_dimensions') as mock_get_dims:
            mock_get_dims.return_value = (800, 600)
            
            # Mock document creation
            with patch('app.services.pdf_service.Document') as mock_document_class:
                mock_new_doc = Mock()
                mock_new_doc.id = 3
                mock_document_class.return_value = mock_new_doc
                
                # Call the function
                result = generate_pdf_from_images(
                    document_ids=[1, 2],
                    pdf_name="test_pdf",
                    user_id=1
                )
                
                # Verify success
                assert result["success"] is True
                assert result["pdf_document_id"] == 3
                assert "test_pdf" in result["message"]
    
    @patch('app.services.pdf_service.SessionLocal')
    def test_generate_pdf_from_images_no_documents(self, mock_session):
        """Test PDF generation with no documents found."""
        mock_db = Mock()
        mock_session.return_value = mock_db
        mock_db.query.return_value.filter.return_value.all.return_value = []
        
        result = generate_pdf_from_images(
            document_ids=[1, 2],
            pdf_name="test_pdf",
            user_id=1
        )
        
        assert "error" in result
        assert "No documents found" in result["error"]
    
    @patch('app.services.pdf_service.SessionLocal')
    @patch('app.services.pdf_service.canvas.Canvas')
    def test_generate_pdf_from_text_success(self, mock_canvas, mock_session):
        """Test successful PDF generation from text."""
        mock_db = Mock()
        mock_session.return_value = mock_db
        
        # Mock document
        mock_doc = Mock()
        mock_doc.id = 1
        mock_db.query.return_value.filter.return_value.first.return_value = mock_doc
        mock_db.query.return_value.filter.return_value.scalar.return_value = "/path/to/file.txt"
        
        # Mock canvas
        mock_canvas_instance = Mock()
        mock_canvas.return_value = mock_canvas_instance
        
        result = generate_pdf_from_text(1, "Test content\nLine 2")
        
        assert result["success"] is True
        assert result["document_id"] == 1
        assert "pdf_path" in result
    
    @patch('app.services.pdf_service.SessionLocal')
    def test_generate_pdf_from_text_no_document(self, mock_session):
        """Test PDF generation with no document found."""
        mock_db = Mock()
        mock_session.return_value = mock_db
        mock_db.query.return_value.filter.return_value.first.return_value = None
        
        result = generate_pdf_from_text(999, "Test content")
        
        assert "error" in result
        assert "Document not found" in result["error"]


if __name__ == "__main__":
    pytest.main([__file__])
