# AI/ML Dependencies for Document Processing
# Add these to your existing requirements.txt

# OCR (Optical Character Recognition)
pytesseract==0.3.10
tesseract==0.1.3

# Computer Vision
opencv-python==********
opencv-contrib-python==********

# Natural Language Processing
spacy==3.7.2
spacy-legacy==3.0.12
spacy-loggers==1.0.5

# Machine Learning
scikit-learn==1.3.2
numpy==1.24.3
pandas==2.1.4

# Deep Learning (Optional)
torch==2.1.1
torchvision==0.16.1
transformers==4.35.2

# Image Processing Enhancement
Pillow==10.1.0  # Already included
scipy==1.11.4

# Text Processing
nltk==3.8.1
textblob==0.17.1

# Document Analysis
pdfplumber==0.9.0
python-docx==1.1.0

# Installation Commands:
# pip install pytesseract opencv-python spacy scikit-learn
# python -m spacy download en_core_web_sm
# 
# For Tesseract OCR:
# Windows: Download from https://github.com/UB-Mannheim/tesseract/wiki
# Linux: sudo apt-get install tesseract-ocr
# Mac: brew install tesseract
