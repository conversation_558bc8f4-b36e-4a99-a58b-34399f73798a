"""
Simple direct test of the multiple image upload functionality
"""

import requests
from PIL import Image
import tempfile
import os

def create_simple_test_images():
    """Create 3 simple test images."""
    images = []
    colors = ['red', 'green', 'blue']
    
    for i, color in enumerate(colors):
        # Create temporary file
        tmp_file = tempfile.NamedTemporaryFile(suffix=f'_{color}.png', delete=False)
        tmp_file.close()
        
        # Create simple colored image
        img = Image.new('RGB', (300, 200), color=color)
        img.save(tmp_file.name)
        
        images.append({
            'path': tmp_file.name,
            'name': f'{color}_test.png'
        })
        
        print(f"✅ Created {color} test image")
    
    return images

def test_multiple_upload():
    """Test multiple image upload directly."""
    print("🧪 DIRECT TEST: Multiple Image Upload")
    print("=" * 40)
    
    # Create test images
    print("\n🖼️ Creating test images...")
    test_images = create_simple_test_images()
    
    # Test the upload endpoint
    print("\n📤 Testing multiple upload endpoint...")
    
    try:
        # Prepare files
        files = []
        for img in test_images:
            with open(img['path'], 'rb') as f:
                files.append(('files', (img['name'], f.read(), 'image/png')))
        
        # Prepare data
        data = {
            'generate_pdf': 'true',
            'pdf_name': 'Direct_Test_PDF'
        }
        
        # Make request
        response = requests.post(
            'http://localhost:8003/api/v1/documents/upload-multiple',
            files=files,
            data=data
        )
        
        print(f"📊 Response Status: {response.status_code}")
        print(f"📋 Response Headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ SUCCESS! Response:")
            print(f"   📁 Uploaded count: {result.get('uploaded_count', 'unknown')}")
            print(f"   📄 PDF result: {result.get('pdf_result', 'none')}")
            print(f"   💬 Message: {result.get('message', 'no message')}")
            return True
        else:
            print(f"❌ FAILED! Status: {response.status_code}")
            print(f"   Error: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ ERROR: {e}")
        return False
    
    finally:
        # Cleanup
        print("\n🧹 Cleaning up...")
        for img in test_images:
            try:
                os.unlink(img['path'])
                print(f"   🗑️ Deleted: {img['name']}")
            except:
                pass

def test_server_endpoints():
    """Test all server endpoints to see what's available."""
    print("\n🔍 TESTING ALL ENDPOINTS")
    print("=" * 30)
    
    base_url = "http://localhost:8003"
    
    endpoints_to_test = [
        "/",
        "/docs",
        "/api/v1/documents",
        "/api/v1/documents/upload-multiple"
    ]
    
    for endpoint in endpoints_to_test:
        try:
            response = requests.get(f"{base_url}{endpoint}")
            print(f"📍 {endpoint}: {response.status_code}")
            if response.status_code == 200 and endpoint == "/":
                print(f"   Content: {response.text[:100]}...")
        except Exception as e:
            print(f"📍 {endpoint}: ERROR - {e}")

if __name__ == "__main__":
    # Test server endpoints first
    test_server_endpoints()
    
    # Test multiple upload
    success = test_multiple_upload()
    
    if success:
        print("\n🎉 MULTIPLE IMAGE UPLOAD IS WORKING!")
    else:
        print("\n❌ MULTIPLE IMAGE UPLOAD HAS ISSUES!")
