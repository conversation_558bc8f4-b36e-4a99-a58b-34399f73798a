"""
Simple PDF Testing Server - No Authentication Required
Standalone server for testing PDF generation functionality
"""

from fastapi import FastAPI, UploadFile, File, Form, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import FileResponse
from typing import List, Optional
import os
import uuid
from PIL import Image
from reportlab.pdfgen import canvas
from reportlab.lib.pagesizes import letter, landscape

# Create FastAPI app with no authentication
app = FastAPI(
    title="Simple PDF Test Server",
    description="No-auth server for testing PDF generation",
    version="1.0.0"
)

# Add CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Create uploads directory
os.makedirs("uploads", exist_ok=True)

# Simple storage
documents = []
doc_id = 1

@app.get("/")
def root():
    return {
        "message": "Simple PDF Test Server - No Authentication Required",
        "status": "running",
        "total_documents": len(documents),
        "instructions": "Go to /docs for testing"
    }

@app.post("/upload")
async def upload_single_image(file: UploadFile = File(...)):
    """Upload a single image - NO AUTH REQUIRED."""
    global doc_id
    
    # Validate file type
    if not file.content_type or not file.content_type.startswith('image/'):
        raise HTTPException(status_code=400, detail="Please upload an image file (PNG, JPG, JPEG)")
    
    # Save file
    filename = file.filename or f"image_{doc_id}"
    file_extension = os.path.splitext(filename)[1] or ".png"
    unique_filename = f"{uuid.uuid4()}{file_extension}"
    file_path = os.path.join("uploads", unique_filename)
    
    with open(file_path, "wb") as buffer:
        content = await file.read()
        buffer.write(content)
    
    # Store document info
    doc = {
        "id": doc_id,
        "name": filename,
        "file_path": file_path,
        "size": len(content),
        "type": "image"
    }
    documents.append(doc)
    doc_id += 1
    
    return {
        "id": doc["id"], 
        "name": doc["name"], 
        "status": "uploaded",
        "size": doc["size"],
        "message": f"✅ Image uploaded successfully! ID: {doc['id']}"
    }

@app.post("/upload-multiple")
async def upload_multiple_images(
    files: List[UploadFile] = File(...),
    generate_pdf: bool = Form(False),
    pdf_name: Optional[str] = Form("Combined_Images")
):
    """Upload multiple images and optionally generate PDF - NO AUTH REQUIRED."""
    global doc_id
    
    uploaded = []
    uploaded_ids = []
    
    # Upload all files
    for file in files:
        try:
            if not file.content_type or not file.content_type.startswith('image/'):
                uploaded.append({
                    "name": file.filename,
                    "status": "failed",
                    "message": "Not an image file"
                })
                continue
            
            filename = file.filename or f"image_{doc_id}"
            file_extension = os.path.splitext(filename)[1] or ".png"
            unique_filename = f"{uuid.uuid4()}{file_extension}"
            file_path = os.path.join("uploads", unique_filename)
            
            with open(file_path, "wb") as buffer:
                content = await file.read()
                buffer.write(content)
            
            doc = {
                "id": doc_id,
                "name": filename,
                "file_path": file_path,
                "size": len(content),
                "type": "image"
            }
            documents.append(doc)
            uploaded_ids.append(doc_id)
            
            uploaded.append({
                "id": doc_id,
                "name": filename,
                "status": "uploaded",
                "size": doc["size"]
            })
            
            doc_id += 1
            
        except Exception as e:
            uploaded.append({
                "name": file.filename or "unknown",
                "status": "failed",
                "message": str(e)
            })
    
    pdf_result = None
    
    # Generate PDF if requested
    if generate_pdf and uploaded_ids:
        try:
            # Get uploaded image documents
            image_docs = [doc for doc in documents if doc["id"] in uploaded_ids]
            
            if image_docs:
                # Create PDF
                pdf_filename = f"{pdf_name}_{uuid.uuid4().hex[:8]}.pdf"
                pdf_path = os.path.join("uploads", pdf_filename)
                
                c = canvas.Canvas(pdf_path, pagesize=letter)
                page_width, page_height = letter
                
                for doc in image_docs:
                    try:
                        # Get image dimensions
                        with Image.open(doc["file_path"]) as img:
                            img_width, img_height = img.size
                        
                        # Calculate scaling to fit page
                        margin = 50
                        available_width = page_width - 2 * margin
                        available_height = page_height - 2 * margin
                        
                        scale_x = available_width / img_width
                        scale_y = available_height / img_height
                        scale = min(scale_x, scale_y)
                        
                        new_width = img_width * scale
                        new_height = img_height * scale
                        
                        # Center the image
                        x = margin + (available_width - new_width) / 2
                        y = margin + (available_height - new_height) / 2
                        
                        # Add image to PDF
                        c.drawImage(doc["file_path"], x, y, width=new_width, height=new_height)
                        c.showPage()
                        
                    except Exception as e:
                        print(f"Error processing image: {e}")
                        continue
                
                c.save()
                
                # Add PDF to documents
                pdf_doc = {
                    "id": doc_id,
                    "name": pdf_filename,
                    "file_path": pdf_path,
                    "size": os.path.getsize(pdf_path),
                    "type": "pdf"
                }
                documents.append(pdf_doc)
                doc_id += 1
                
                pdf_result = {
                    "pdf_id": pdf_doc["id"],
                    "pdf_name": pdf_filename,
                    "pdf_size": pdf_doc["size"],
                    "pages": len(image_docs),
                    "status": "generated",
                    "download_url": f"/download/{pdf_filename}"
                }
                
        except Exception as e:
            pdf_result = {"status": "failed", "error": str(e)}
    
    return {
        "uploaded_files": uploaded,
        "uploaded_count": len([u for u in uploaded if u["status"] == "uploaded"]),
        "pdf_result": pdf_result,
        "message": f"✅ Processed {len(uploaded)} files" + (f", PDF generated!" if pdf_result and pdf_result.get("status") == "generated" else "")
    }

@app.post("/images-to-pdf")
def convert_images_to_pdf(request: dict):
    """Convert existing images to PDF - NO AUTH REQUIRED."""
    global doc_id
    
    document_ids = request.get("document_ids", [])
    pdf_name = request.get("pdf_name", "converted_images")
    page_orientation = request.get("page_orientation", "portrait")
    image_fit = request.get("image_fit", "contain")
    
    if not document_ids:
        raise HTTPException(status_code=400, detail="Please provide document_ids")
    
    # Get documents
    selected_docs = [doc for doc in documents if doc["id"] in document_ids and doc["type"] == "image"]
    
    if not selected_docs:
        raise HTTPException(status_code=404, detail="No image documents found with provided IDs")
    
    try:
        # Set page size
        pagesize = landscape(letter) if page_orientation == "landscape" else letter
        
        # Create PDF
        pdf_filename = f"{pdf_name}_{uuid.uuid4().hex[:8]}.pdf"
        pdf_path = os.path.join("uploads", pdf_filename)
        
        c = canvas.Canvas(pdf_path, pagesize=pagesize)
        page_width, page_height = pagesize
        
        for doc in selected_docs:
            try:
                with Image.open(doc["file_path"]) as img:
                    img_width, img_height = img.size
                
                # Calculate positioning based on fit mode
                margin = 50
                available_width = page_width - 2 * margin
                available_height = page_height - 2 * margin
                
                if image_fit == "contain":
                    scale_x = available_width / img_width
                    scale_y = available_height / img_height
                    scale = min(scale_x, scale_y)
                    new_width = img_width * scale
                    new_height = img_height * scale
                    x = margin + (available_width - new_width) / 2
                    y = margin + (available_height - new_height) / 2
                elif image_fit == "fill":
                    new_width = available_width
                    new_height = available_height
                    x = margin
                    y = margin
                else:  # cover
                    scale_x = available_width / img_width
                    scale_y = available_height / img_height
                    scale = max(scale_x, scale_y)
                    new_width = img_width * scale
                    new_height = img_height * scale
                    x = margin + (available_width - new_width) / 2
                    y = margin + (available_height - new_height) / 2
                
                c.drawImage(doc["file_path"], x, y, width=new_width, height=new_height)
                c.showPage()
                
            except Exception as e:
                print(f"Error processing image: {e}")
                continue
        
        c.save()
        
        # Add PDF to documents
        pdf_doc = {
            "id": doc_id,
            "name": pdf_filename,
            "file_path": pdf_path,
            "size": os.path.getsize(pdf_path),
            "type": "pdf"
        }
        documents.append(pdf_doc)
        doc_id += 1
        
        return {
            "pdf_id": pdf_doc["id"],
            "pdf_name": pdf_filename,
            "pdf_size": pdf_doc["size"],
            "pages": len(selected_docs),
            "orientation": page_orientation,
            "image_fit": image_fit,
            "status": "generated",
            "message": f"✅ PDF created from {len(selected_docs)} images",
            "download_url": f"/download/{pdf_filename}"
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"PDF generation failed: {str(e)}")

@app.get("/documents")
def get_documents():
    """Get all documents - NO AUTH REQUIRED."""
    images = [d for d in documents if d["type"] == "image"]
    pdfs = [d for d in documents if d["type"] == "pdf"]
    
    return {
        "documents": documents,
        "summary": {
            "total": len(documents),
            "images": len(images),
            "pdfs": len(pdfs)
        },
        "message": f"Found {len(images)} images and {len(pdfs)} PDFs"
    }

@app.get("/download/{filename}")
def download_file(filename: str):
    """Download a file - NO AUTH REQUIRED."""
    file_path = os.path.join("uploads", filename)
    if os.path.exists(file_path):
        return FileResponse(file_path, filename=filename)
    else:
        raise HTTPException(status_code=404, detail="File not found")

if __name__ == "__main__":
    import uvicorn
    print("🚀 Starting Simple PDF Test Server (No Authentication)")
    print("📖 Go to http://localhost:8001/docs to test")
    uvicorn.run(app, host="0.0.0.0", port=8001)
