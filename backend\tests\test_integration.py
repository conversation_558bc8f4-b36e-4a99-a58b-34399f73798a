import pytest
import tempfile
import os
from fastapi.testclient import <PERSON><PERSON><PERSON>
from unittest.mock import patch, <PERSON>ck
from PIL import Image
import io

from app.main import app
from app.database import get_db
from app.services.auth_service import get_current_user


class TestIntegration:
    """Integration tests for the complete upload and PDF generation workflow."""
    
    def setup_method(self):
        """Setup test client and mocks."""
        self.client = TestClient(app)
        
        # Mock user
        self.mock_user = Mock()
        self.mock_user.id = 1
        self.mock_user.email = "<EMAIL>"
        
        # Mock database
        self.mock_db = Mock()
    
    def create_test_image(self, filename="test.png", size=(800, 600), format="PNG"):
        """Create a test image file."""
        img = Image.new('RGB', size, color='red')
        img_bytes = io.BytesIO()
        img.save(img_bytes, format=format)
        img_bytes.seek(0)
        return img_bytes
    
    @patch('app.api.documents.get_current_user')
    @patch('app.api.documents.get_db')
    @patch('app.services.document_service.save_document')
    def test_complete_upload_workflow(self, mock_save_doc, mock_get_db, mock_get_user):
        """Test complete workflow from upload to document creation."""
        # Setup mocks
        mock_get_user.return_value = self.mock_user
        mock_get_db.return_value = self.mock_db
        self.mock_db.query.return_value.filter.return_value.scalar.return_value = 1
        
        # Mock document creation
        mock_document = Mock()
        mock_document.file_path = "/path/to/test.png"
        mock_save_doc.return_value = mock_document
        self.mock_db.query.return_value.filter.return_value.scalar.return_value = 123
        
        # Create test image
        test_image = self.create_test_image()
        
        # Upload single document
        response = self.client.post(
            "/api/v1/documents/upload",
            files={"file": ("test.png", test_image, "image/png")}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["name"] == "test.png"
        assert data["status"] == "pending"
        assert "successfully" in data["message"]
        
        # Verify save_document was called
        mock_save_doc.assert_called_once()
    
    @patch('app.api.documents.get_current_user')
    @patch('app.api.documents.get_db')
    @patch('app.services.document_service.save_document')
    def test_multiple_upload_with_pdf_generation_workflow(self, mock_save_doc, mock_get_db, mock_get_user):
        """Test complete workflow for multiple upload with PDF generation."""
        # Setup mocks
        mock_get_user.return_value = self.mock_user
        mock_get_db.return_value = self.mock_db
        self.mock_db.query.return_value.filter.return_value.scalar.return_value = 1
        
        # Mock document creation
        mock_document1 = Mock()
        mock_document1.file_path = "/path/to/test1.png"
        mock_document2 = Mock()
        mock_document2.file_path = "/path/to/test2.jpg"
        
        mock_save_doc.side_effect = [mock_document1, mock_document2]
        self.mock_db.query.return_value.filter.return_value.scalar.side_effect = [123, 124]
        
        # Mock PDF generation task
        with patch('app.services.pdf_service.generate_pdf_from_images') as mock_pdf_task:
            mock_task = Mock()
            mock_task.id = "task-123"
            mock_pdf_task.delay.return_value = mock_task
            
            # Create test images
            test_image1 = self.create_test_image("test1.png")
            test_image2 = self.create_test_image("test2.jpg", format="JPEG")
            
            # Upload multiple documents with PDF generation
            response = self.client.post(
                "/api/v1/documents/upload-multiple",
                files=[
                    ("files", ("test1.png", test_image1, "image/png")),
                    ("files", ("test2.jpg", test_image2, "image/jpeg"))
                ],
                data={
                    "generate_pdf": "true",
                    "pdf_name": "combined_images"
                }
            )
            
            assert response.status_code == 200
            data = response.json()
            assert len(data["uploaded_files"]) == 2
            assert data["pdf_generation_status"] == "processing"
            assert data["batch_id"] == "task-123"
            assert "2 files successfully" in data["message"]
    
    @patch('app.api.documents.get_current_user')
    @patch('app.api.documents.get_db')
    def test_images_to_pdf_conversion_workflow(self, mock_get_db, mock_get_user):
        """Test complete workflow for converting existing images to PDF."""
        # Setup mocks
        mock_get_user.return_value = self.mock_user
        mock_get_db.return_value = self.mock_db
        self.mock_db.query.return_value.filter.return_value.scalar.return_value = 1
        
        # Mock existing documents
        mock_doc1 = Mock()
        mock_doc1.id = 1
        mock_doc2 = Mock()
        mock_doc2.id = 2
        self.mock_db.query.return_value.filter.return_value.all.return_value = [mock_doc1, mock_doc2]
        
        # Mock PDF generation task
        with patch('app.services.pdf_service.generate_pdf_from_images') as mock_pdf_task:
            mock_task = Mock()
            mock_task.id = "task-456"
            mock_pdf_task.delay.return_value = mock_task
            
            # Convert images to PDF
            response = self.client.post(
                "/api/v1/documents/images-to-pdf",
                json={
                    "document_ids": [1, 2],
                    "pdf_name": "converted_images",
                    "page_orientation": "landscape",
                    "image_fit": "cover"
                }
            )
            
            assert response.status_code == 200
            data = response.json()
            assert data["pdf_name"] == "converted_images"
            assert data["status"] == "processing"
            assert "2 images" in data["message"]
            
            # Verify PDF task was called with correct parameters
            mock_pdf_task.delay.assert_called_once_with(
                document_ids=[1, 2],
                pdf_name="converted_images",
                page_orientation="landscape",
                image_fit="cover",
                user_id=1
            )
    
    @patch('app.api.documents.get_current_user')
    @patch('app.api.documents.get_db')
    def test_error_handling_workflow(self, mock_get_db, mock_get_user):
        """Test error handling in various scenarios."""
        # Setup mocks
        mock_get_user.return_value = self.mock_user
        mock_get_db.return_value = self.mock_db
        self.mock_db.query.return_value.filter.return_value.scalar.return_value = 1
        
        # Test 1: Invalid document IDs for PDF conversion
        self.mock_db.query.return_value.filter.return_value.all.return_value = [Mock(id=1)]  # Only 1 doc found
        
        response = self.client.post(
            "/api/v1/documents/images-to-pdf",
            json={
                "document_ids": [1, 999],  # 999 doesn't exist
                "pdf_name": "test_pdf",
                "page_orientation": "portrait",
                "image_fit": "contain"
            }
        )
        
        assert response.status_code == 404
        assert "not found" in response.json()["detail"]
        
        # Test 2: Empty document list
        self.mock_db.query.return_value.filter.return_value.all.return_value = []
        
        response = self.client.post(
            "/api/v1/documents/images-to-pdf",
            json={
                "document_ids": [1, 2],
                "pdf_name": "test_pdf",
                "page_orientation": "portrait",
                "image_fit": "contain"
            }
        )
        
        assert response.status_code == 404
    
    @patch('app.api.documents.get_current_user')
    @patch('app.api.documents.get_db')
    def test_document_retrieval_workflow(self, mock_get_db, mock_get_user):
        """Test document retrieval and deletion workflow."""
        # Setup mocks
        mock_get_user.return_value = self.mock_user
        mock_get_db.return_value = self.mock_db
        
        # Test document retrieval
        mock_document = Mock()
        mock_document.id = 1
        mock_document.name = "test.png"
        mock_document.status = "completed"
        self.mock_db.query.return_value.filter.return_value.first.return_value = mock_document
        
        response = self.client.get("/api/v1/documents/1")
        assert response.status_code == 200
        
        # Test document deletion
        response = self.client.delete("/api/v1/documents/1")
        assert response.status_code == 200
        data = response.json()
        assert "deleted successfully" in data["message"]
        
        # Verify database operations
        self.mock_db.delete.assert_called_once_with(mock_document)
        self.mock_db.commit.assert_called()
    
    def test_file_validation_workflow(self):
        """Test file validation during upload."""
        # Test with invalid file type
        invalid_file = io.BytesIO(b"not an image")
        
        with patch('app.api.documents.get_current_user') as mock_get_user, \
             patch('app.api.documents.get_db') as mock_get_db, \
             patch('app.services.document_service.save_document') as mock_save_doc:
            
            mock_get_user.return_value = self.mock_user
            mock_get_db.return_value = self.mock_db
            
            # Mock validation error
            from fastapi import HTTPException
            mock_save_doc.side_effect = HTTPException(status_code=400, detail="Invalid file type")
            
            response = self.client.post(
                "/api/v1/documents/upload",
                files={"file": ("test.exe", invalid_file, "application/octet-stream")}
            )
            
            # Should handle the error gracefully
            # Note: The actual response depends on how the exception is handled in the endpoint


if __name__ == "__main__":
    pytest.main([__file__])
