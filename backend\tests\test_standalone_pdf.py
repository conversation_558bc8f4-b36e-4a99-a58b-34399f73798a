"""
Standalone tests for PDF functionality without external dependencies.
These tests verify the core logic of our PDF generation functions.
"""

import os
import tempfile
from PIL import Image


def get_image_dimensions_standalone(image_path: str) -> tuple[int, int]:
    """Standalone version of get_image_dimensions for testing."""
    with Image.open(image_path) as img:
        return img.size


def calculate_image_position_standalone(img_width: int, img_height: int, page_width: float, page_height: float, 
                                      fit_mode: str = "contain") -> tuple[float, float, float, float]:
    """Standalone version of calculate_image_position for testing."""
    margin = 50
    available_width = page_width - 2 * margin
    available_height = page_height - 2 * margin
    
    if fit_mode == "contain":
        # Scale image to fit within page while maintaining aspect ratio
        scale_x = available_width / img_width
        scale_y = available_height / img_height
        scale = min(scale_x, scale_y)
        
        new_width = img_width * scale
        new_height = img_height * scale
        
        # Center the image
        x = margin + (available_width - new_width) / 2
        y = margin + (available_height - new_height) / 2
        
        return x, y, new_width, new_height
    
    elif fit_mode == "fill":
        # Fill entire page (may crop image)
        return margin, margin, available_width, available_height
    
    else:  # cover
        # Scale to cover entire page while maintaining aspect ratio
        scale_x = available_width / img_width
        scale_y = available_height / img_height
        scale = max(scale_x, scale_y)
        
        new_width = img_width * scale
        new_height = img_height * scale
        
        # Center the image (may be cropped)
        x = margin + (available_width - new_width) / 2
        y = margin + (available_height - new_height) / 2
        
        return x, y, new_width, new_height


def test_image_dimensions():
    """Test image dimension detection."""
    print("🧪 Testing image dimensions...")
    
    test_cases = [
        (800, 600),
        (1920, 1080),
        (100, 100),
        (300, 400)
    ]
    
    for width, height in test_cases:
        # Create temporary file manually to avoid Windows file locking issues
        tmp_file = tempfile.NamedTemporaryFile(suffix='.png', delete=False)
        tmp_file.close()  # Close the file handle immediately

        try:
            # Create test image
            img = Image.new('RGB', (width, height), color='red')
            img.save(tmp_file.name)

            # Test dimension detection
            detected_width, detected_height = get_image_dimensions_standalone(tmp_file.name)

            assert detected_width == width, f"Width mismatch: expected {width}, got {detected_width}"
            assert detected_height == height, f"Height mismatch: expected {height}, got {detected_height}"

            print(f"  ✅ {width}x{height} image dimensions detected correctly")

        finally:
            try:
                if os.path.exists(tmp_file.name):
                    os.unlink(tmp_file.name)
            except:
                pass  # Ignore cleanup errors


def test_image_positioning():
    """Test image positioning calculations."""
    print("🧪 Testing image positioning...")
    
    # Test contain mode with smaller image (should fit within available space)
    x, y, width, height = calculate_image_position_standalone(400, 300, 612, 792, "contain")
    available_width = 512  # 612 - 2*50
    available_height = 692  # 792 - 2*50

    # Image should fit within available space
    assert width <= available_width, f"Width should fit within {available_width}, got {width}"
    assert height <= available_height, f"Height should fit within {available_height}, got {height}"
    assert x >= 50, f"X position should have margin, got {x}"
    assert y >= 50, f"Y position should have margin, got {y}"

    # Check aspect ratio is maintained
    original_ratio = 400 / 300
    scaled_ratio = width / height
    assert abs(original_ratio - scaled_ratio) < 0.01, f"Aspect ratio not maintained: {original_ratio} vs {scaled_ratio}"
    print("  ✅ Contain mode with smaller image works correctly")
    
    # Test contain mode with larger image
    x, y, width, height = calculate_image_position_standalone(1000, 800, 612, 792, "contain")
    assert width < 1000, f"Width should be scaled down from 1000, got {width}"
    assert height < 800, f"Height should be scaled down from 800, got {height}"
    # Check aspect ratio is maintained
    original_ratio = 1000 / 800
    scaled_ratio = width / height
    assert abs(original_ratio - scaled_ratio) < 0.01, f"Aspect ratio not maintained: {original_ratio} vs {scaled_ratio}"
    print("  ✅ Contain mode with larger image scales correctly")
    
    # Test fill mode
    x, y, width, height = calculate_image_position_standalone(400, 300, 612, 792, "fill")
    assert x == 50, f"X should be margin (50), got {x}"
    assert y == 50, f"Y should be margin (50), got {y}"
    assert width == 512, f"Width should be 512 (612-2*50), got {width}"
    assert height == 692, f"Height should be 692 (792-2*50), got {height}"
    print("  ✅ Fill mode works correctly")
    
    # Test cover mode
    x, y, width, height = calculate_image_position_standalone(400, 300, 612, 792, "cover")
    available_width = 512
    available_height = 692
    assert width >= available_width or height >= available_height, "Cover mode should fill at least one dimension"
    # Check aspect ratio is maintained
    original_ratio = 400 / 300
    scaled_ratio = width / height
    assert abs(original_ratio - scaled_ratio) < 0.01, f"Aspect ratio not maintained in cover mode"
    print("  ✅ Cover mode works correctly")


def test_edge_cases():
    """Test edge cases and boundary conditions."""
    print("🧪 Testing edge cases...")
    
    # Very small image (should be scaled up to fit better in the page)
    x, y, width, height = calculate_image_position_standalone(10, 10, 612, 792, "contain")
    # Small images will be scaled up in contain mode, so just check they're positioned correctly
    assert x >= 50, "X position should have margin"
    assert y >= 50, "Y position should have margin"
    assert width == height, "Square image should remain square"
    print("  ✅ Very small image handled correctly")
    
    # Very wide image
    x, y, width, height = calculate_image_position_standalone(2000, 100, 612, 792, "contain")
    assert width < 2000, "Very wide image should be scaled down"
    assert height < 100, "Height should be scaled proportionally"
    print("  ✅ Very wide image handled correctly")
    
    # Very tall image
    x, y, width, height = calculate_image_position_standalone(100, 2000, 612, 792, "contain")
    assert width < 100, "Width should be scaled proportionally"
    assert height < 2000, "Very tall image should be scaled down"
    print("  ✅ Very tall image handled correctly")
    
    # Square image
    x, y, width, height = calculate_image_position_standalone(500, 500, 612, 792, "contain")
    assert abs(width - height) < 0.01, "Square image should remain square"
    print("  ✅ Square image handled correctly")


def test_centering():
    """Test that images are properly centered."""
    print("🧪 Testing image centering...")
    
    # Small image should be centered
    x, y, width, height = calculate_image_position_standalone(200, 150, 612, 792, "contain")
    
    available_width = 512  # 612 - 2*50
    available_height = 692  # 792 - 2*50
    
    # Calculate expected centering
    expected_x = 50 + (available_width - width) / 2
    expected_y = 50 + (available_height - height) / 2
    
    assert abs(x - expected_x) < 0.01, f"X centering incorrect: expected {expected_x}, got {x}"
    assert abs(y - expected_y) < 0.01, f"Y centering incorrect: expected {expected_y}, got {y}"
    print("  ✅ Image centering works correctly")


def test_different_page_sizes():
    """Test with different page sizes."""
    print("🧪 Testing different page sizes...")
    
    # A4 size (595, 842)
    x, y, width, height = calculate_image_position_standalone(400, 300, 595, 842, "contain")
    assert x >= 50 and y >= 50, "Should respect margins on A4"
    assert width <= 495 and height <= 742, "Should fit within A4 page"
    print("  ✅ A4 page size works correctly")
    
    # Letter size (612, 792)
    x, y, width, height = calculate_image_position_standalone(400, 300, 612, 792, "contain")
    assert x >= 50 and y >= 50, "Should respect margins on Letter"
    assert width <= 512 and height <= 692, "Should fit within Letter page"
    print("  ✅ Letter page size works correctly")


def run_all_tests():
    """Run all tests."""
    print("🚀 Starting PDF functionality tests...\n")
    
    try:
        test_image_dimensions()
        print()
        
        test_image_positioning()
        print()
        
        test_edge_cases()
        print()
        
        test_centering()
        print()
        
        test_different_page_sizes()
        print()
        
        print("🎉 All tests passed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False


if __name__ == "__main__":
    success = run_all_tests()
    exit(0 if success else 1)
