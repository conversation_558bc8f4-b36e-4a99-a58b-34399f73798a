<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Multiple Image Upload Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        input[type="file"] {
            width: 100%;
            padding: 10px;
            border: 2px dashed #ddd;
            border-radius: 5px;
            background: #fafafa;
        }
        input[type="text"] {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            box-sizing: border-box;
        }
        .checkbox-group {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        button {
            background: #007bff;
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            width: 100%;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            display: none;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .file-info {
            margin-top: 10px;
            padding: 10px;
            background: #e9ecef;
            border-radius: 5px;
            font-size: 14px;
        }
        .tip {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📚 Multiple Image Upload & PDF Generation Test</h1>
        
        <div class="tip">
            <strong>💡 TIP:</strong> Hold <kbd>Ctrl</kbd> (Windows) or <kbd>Cmd</kbd> (Mac) while clicking to select multiple images at once!
        </div>

        <form id="uploadForm">
            <div class="form-group">
                <label for="files">Select Multiple Images:</label>
                <input type="file" id="files" name="files" multiple accept="image/*" required>
                <div id="fileInfo" class="file-info" style="display: none;"></div>
            </div>

            <div class="form-group">
                <div class="checkbox-group">
                    <input type="checkbox" id="generatePdf" name="generate_pdf" checked>
                    <label for="generatePdf">Generate PDF from uploaded images</label>
                </div>
            </div>

            <div class="form-group">
                <label for="pdfName">PDF Name (optional):</label>
                <input type="text" id="pdfName" name="pdf_name" value="Test_Combined_PDF" placeholder="Enter PDF name">
            </div>

            <button type="submit" id="submitBtn">Upload Images & Generate PDF</button>
        </form>

        <div id="result" class="result"></div>
    </div>

    <script>
        const form = document.getElementById('uploadForm');
        const filesInput = document.getElementById('files');
        const fileInfo = document.getElementById('fileInfo');
        const result = document.getElementById('result');
        const submitBtn = document.getElementById('submitBtn');

        // Show file information when files are selected
        filesInput.addEventListener('change', function() {
            const files = this.files;
            if (files.length > 0) {
                let info = `Selected ${files.length} file(s):\n`;
                for (let i = 0; i < files.length; i++) {
                    const file = files[i];
                    const sizeKB = (file.size / 1024).toFixed(1);
                    info += `• ${file.name} (${sizeKB} KB)\n`;
                }
                fileInfo.textContent = info;
                fileInfo.style.display = 'block';
            } else {
                fileInfo.style.display = 'none';
            }
        });

        // Handle form submission
        form.addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const files = filesInput.files;
            if (files.length === 0) {
                showResult('Please select at least one image file.', 'error');
                return;
            }

            // Disable submit button
            submitBtn.disabled = true;
            submitBtn.textContent = 'Uploading...';

            // Prepare form data
            const formData = new FormData();
            
            // Add all files
            for (let i = 0; i < files.length; i++) {
                formData.append('files', files[i]);
            }
            
            // Add other form data
            formData.append('generate_pdf', document.getElementById('generatePdf').checked);
            formData.append('pdf_name', document.getElementById('pdfName').value || 'Test_Combined_PDF');

            try {
                // Send request
                const response = await fetch('http://localhost:8001/api/v1/documents/upload-multiple', {
                    method: 'POST',
                    body: formData
                });

                const data = await response.json();

                if (response.ok) {
                    let message = `✅ Success! Uploaded ${data.uploaded_count} files.\n`;
                    message += `Message: ${data.message}\n`;
                    
                    if (data.pdf_result && data.pdf_result.status === 'generated') {
                        message += `\n📄 PDF Generated:\n`;
                        message += `• Name: ${data.pdf_result.pdf_name}\n`;
                        message += `• Size: ${data.pdf_result.pdf_size} bytes\n`;
                        message += `• Pages: ${data.pdf_result.pages}\n`;
                        message += `• Download: ${data.pdf_result.download_url}`;
                    }
                    
                    showResult(message, 'success');
                } else {
                    showResult(`❌ Error: ${data.detail || 'Upload failed'}`, 'error');
                }
            } catch (error) {
                showResult(`❌ Network Error: ${error.message}`, 'error');
            } finally {
                // Re-enable submit button
                submitBtn.disabled = false;
                submitBtn.textContent = 'Upload Images & Generate PDF';
            }
        });

        function showResult(message, type) {
            result.textContent = message;
            result.className = `result ${type}`;
            result.style.display = 'block';
            
            // Scroll to result
            result.scrollIntoView({ behavior: 'smooth' });
        }
    </script>
</body>
</html>
