"""
Minimal working test server for PDF generation functionality
"""

from fastapi import FastAP<PERSON>, UploadFile, File, Form, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from typing import List, Optional
import os
import uuid
from PIL import Image
from reportlab.pdfgen import canvas
from reportlab.lib.pagesizes import letter, landscape

# Create FastAPI app
app = FastAPI(title="PDF Test Server", version="1.0.0")

# Add CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Create uploads directory
os.makedirs("uploads", exist_ok=True)

# Simple storage
documents = []
doc_id = 1

@app.get("/")
def root():
    return {"message": "PDF Test Server Running", "status": "ok"}

@app.get("/test")
def test():
    return {"message": "Test endpoint working", "documents_count": len(documents)}

@app.post("/upload")
async def upload_file(file: UploadFile = File(...)):
    global doc_id
    
    # Save file
    filename = file.filename or "unknown"
    file_extension = os.path.splitext(filename)[1]
    unique_filename = f"{uuid.uuid4()}{file_extension}"
    file_path = os.path.join("uploads", unique_filename)
    
    with open(file_path, "wb") as buffer:
        content = await file.read()
        buffer.write(content)
    
    # Store document info
    doc = {
        "id": doc_id,
        "name": filename,
        "file_path": file_path,
        "mime_type": file.content_type,
        "size": len(content)
    }
    documents.append(doc)
    doc_id += 1
    
    return {"id": doc["id"], "name": doc["name"], "status": "uploaded"}

@app.post("/upload-multiple")
async def upload_multiple(
    files: List[UploadFile] = File(...),
    generate_pdf: bool = Form(False),
    pdf_name: Optional[str] = Form(None)
):
    global doc_id
    
    uploaded = []
    
    # Upload all files
    for file in files:
        filename = file.filename or "unknown"
        file_extension = os.path.splitext(filename)[1]
        unique_filename = f"{uuid.uuid4()}{file_extension}"
        file_path = os.path.join("uploads", unique_filename)
        
        with open(file_path, "wb") as buffer:
            content = await file.read()
            buffer.write(content)
        
        doc = {
            "id": doc_id,
            "name": filename,
            "file_path": file_path,
            "mime_type": file.content_type,
            "size": len(content)
        }
        documents.append(doc)
        uploaded.append(doc["id"])
        doc_id += 1
    
    pdf_status = "not_requested"
    
    # Generate PDF if requested
    if generate_pdf and pdf_name and uploaded:
        try:
            # Get image documents
            image_docs = [doc for doc in documents if doc["id"] in uploaded and doc["mime_type"].startswith("image/")]
            
            if image_docs:
                # Create PDF
                pdf_filename = f"{uuid.uuid4()}_{pdf_name}.pdf"
                pdf_path = os.path.join("uploads", pdf_filename)
                
                c = canvas.Canvas(pdf_path, pagesize=letter)
                page_width, page_height = letter
                
                for doc in image_docs:
                    try:
                        # Simple positioning - center image on page
                        with Image.open(doc["file_path"]) as img:
                            img_width, img_height = img.size
                        
                        # Scale to fit page with margins
                        margin = 50
                        available_width = page_width - 2 * margin
                        available_height = page_height - 2 * margin
                        
                        scale_x = available_width / img_width
                        scale_y = available_height / img_height
                        scale = min(scale_x, scale_y)
                        
                        new_width = img_width * scale
                        new_height = img_height * scale
                        
                        x = margin + (available_width - new_width) / 2
                        y = margin + (available_height - new_height) / 2
                        
                        c.drawImage(doc["file_path"], x, y, width=new_width, height=new_height)
                        c.showPage()
                        
                    except Exception as e:
                        print(f"Error processing image: {e}")
                        continue
                
                c.save()
                
                # Add PDF to documents
                pdf_doc = {
                    "id": doc_id,
                    "name": f"{pdf_name}.pdf",
                    "file_path": pdf_path,
                    "mime_type": "application/pdf",
                    "size": os.path.getsize(pdf_path)
                }
                documents.append(pdf_doc)
                doc_id += 1
                
                pdf_status = "generated"
            else:
                pdf_status = "no_images"
                
        except Exception as e:
            pdf_status = f"error: {str(e)}"
    
    return {
        "uploaded_count": len(uploaded),
        "pdf_status": pdf_status,
        "message": f"Uploaded {len(uploaded)} files"
    }

@app.get("/documents")
def get_documents():
    return {"documents": documents, "count": len(documents)}

@app.post("/images-to-pdf")
def convert_to_pdf(request: dict):
    global doc_id
    
    document_ids = request.get("document_ids", [])
    pdf_name = request.get("pdf_name", "converted")
    
    if not document_ids:
        raise HTTPException(status_code=400, detail="No document IDs provided")
    
    # Get documents
    selected_docs = [doc for doc in documents if doc["id"] in document_ids]
    image_docs = [doc for doc in selected_docs if doc["mime_type"].startswith("image/")]
    
    if not image_docs:
        raise HTTPException(status_code=400, detail="No image documents found")
    
    try:
        # Create PDF
        pdf_filename = f"{uuid.uuid4()}_{pdf_name}.pdf"
        pdf_path = os.path.join("uploads", pdf_filename)
        
        c = canvas.Canvas(pdf_path, pagesize=letter)
        page_width, page_height = letter
        
        for doc in image_docs:
            try:
                with Image.open(doc["file_path"]) as img:
                    img_width, img_height = img.size
                
                # Scale to fit page
                margin = 50
                available_width = page_width - 2 * margin
                available_height = page_height - 2 * margin
                
                scale_x = available_width / img_width
                scale_y = available_height / img_height
                scale = min(scale_x, scale_y)
                
                new_width = img_width * scale
                new_height = img_height * scale
                
                x = margin + (available_width - new_width) / 2
                y = margin + (available_height - new_height) / 2
                
                c.drawImage(doc["file_path"], x, y, width=new_width, height=new_height)
                c.showPage()
                
            except Exception as e:
                print(f"Error processing image: {e}")
                continue
        
        c.save()
        
        # Add PDF to documents
        pdf_doc = {
            "id": doc_id,
            "name": f"{pdf_name}.pdf",
            "file_path": pdf_path,
            "mime_type": "application/pdf",
            "size": os.path.getsize(pdf_path)
        }
        documents.append(pdf_doc)
        doc_id += 1
        
        return {
            "pdf_id": pdf_doc["id"],
            "pdf_name": pdf_doc["name"],
            "status": "generated",
            "message": f"PDF created from {len(image_docs)} images"
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"PDF generation failed: {str(e)}")

if __name__ == "__main__":
    import uvicorn
    print("🚀 Starting Minimal PDF Test Server...")
    print("📋 Endpoints:")
    print("   • GET  / - Server status")
    print("   • GET  /test - Test endpoint")
    print("   • POST /upload - Single file upload")
    print("   • POST /upload-multiple - Multiple files + PDF generation")
    print("   • POST /images-to-pdf - Convert existing images to PDF")
    print("   • GET  /documents - List all documents")
    print("   • Docs: http://localhost:8000/docs")
    print("\n🧪 Ready for testing!")
    
    uvicorn.run(app, host="0.0.0.0", port=8000)
