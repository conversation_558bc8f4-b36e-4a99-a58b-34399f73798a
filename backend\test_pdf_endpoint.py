"""
Test the PDF endpoint to demonstrate correct usage
"""

import requests
from PIL import Image
import tempfile
import os
import json

def create_test_image(color='red', size=(300, 300)):
    """Create a test image."""
    tmp_file = tempfile.NamedTemporaryFile(suffix='.png', delete=False)
    tmp_file.close()
    
    img = Image.new('RGB', size, color=color)
    img.save(tmp_file.name)
    
    return tmp_file.name

def test_complete_workflow():
    """Test the complete workflow step by step."""
    base_url = "http://localhost:8001"
    
    print("🧪 Testing Complete PDF Generation Workflow")
    print("=" * 50)
    
    # Step 1: Check server
    print("\n📡 Step 1: Check server status")
    try:
        response = requests.get(f"{base_url}/")
        print(f"✅ Server response: {response.json()}")
    except Exception as e:
        print(f"❌ Server not accessible: {e}")
        return False
    
    # Step 2: Upload images
    print("\n📁 Step 2: Upload test images")
    
    # Create test images
    image_paths = []
    colors = ['red', 'green', 'blue']
    
    for color in colors:
        path = create_test_image(color)
        image_paths.append(path)
        print(f"   Created {color} image: {os.path.basename(path)}")
    
    # Upload images one by one
    uploaded_ids = []
    
    for i, img_path in enumerate(image_paths):
        try:
            with open(img_path, 'rb') as f:
                files = {'file': (f'{colors[i]}_test.png', f, 'image/png')}
                response = requests.post(f"{base_url}/upload", files=files)
            
            if response.status_code == 200:
                result = response.json()
                uploaded_ids.append(result['id'])
                print(f"   ✅ Uploaded {colors[i]} image - ID: {result['id']}")
            else:
                print(f"   ❌ Failed to upload {colors[i]} image: {response.text}")
                
        except Exception as e:
            print(f"   ❌ Error uploading {colors[i]} image: {e}")
    
    # Step 3: Check documents
    print(f"\n📋 Step 3: Check uploaded documents")
    try:
        response = requests.get(f"{base_url}/documents")
        if response.status_code == 200:
            result = response.json()
            documents = result.get('documents', [])
            print(f"   ✅ Found {len(documents)} documents:")
            for doc in documents:
                print(f"      ID: {doc['id']} - {doc['name']} ({doc['type']})")
        else:
            print(f"   ❌ Failed to get documents: {response.text}")
    except Exception as e:
        print(f"   ❌ Error getting documents: {e}")
    
    # Step 4: Test images-to-pdf with correct format
    if uploaded_ids:
        print(f"\n🖼️➡️📄 Step 4: Convert images to PDF")
        
        # Test 1: Minimal request
        print("   Test 1: Minimal request")
        try:
            data = {
                "document_ids": uploaded_ids[:2]  # Use first 2 images
            }
            
            print(f"   Sending request: {json.dumps(data, indent=2)}")
            
            response = requests.post(f"{base_url}/images-to-pdf", json=data)
            
            if response.status_code == 200:
                result = response.json()
                print(f"   ✅ PDF generated successfully!")
                print(f"      PDF ID: {result['pdf_id']}")
                print(f"      PDF Name: {result['pdf_name']}")
                print(f"      PDF Size: {result['pdf_size']} bytes")
                print(f"      Pages: {result['pages']}")
            else:
                print(f"   ❌ PDF generation failed: {response.status_code}")
                print(f"      Error: {response.text}")
                
        except Exception as e:
            print(f"   ❌ Error in PDF generation: {e}")
        
        # Test 2: Full request with options
        print("\n   Test 2: Full request with options")
        try:
            data = {
                "document_ids": uploaded_ids,  # Use all images
                "pdf_name": "Complete_Test_PDF",
                "page_orientation": "portrait",
                "image_fit": "contain"
            }
            
            print(f"   Sending request: {json.dumps(data, indent=2)}")
            
            response = requests.post(f"{base_url}/images-to-pdf", json=data)
            
            if response.status_code == 200:
                result = response.json()
                print(f"   ✅ Full PDF generated successfully!")
                print(f"      PDF ID: {result['pdf_id']}")
                print(f"      PDF Name: {result['pdf_name']}")
                print(f"      PDF Size: {result['pdf_size']} bytes")
                print(f"      Pages: {result['pages']}")
                print(f"      Download URL: {result['download_url']}")
            else:
                print(f"   ❌ Full PDF generation failed: {response.status_code}")
                print(f"      Error: {response.text}")
                
        except Exception as e:
            print(f"   ❌ Error in full PDF generation: {e}")
    
    # Step 5: Final document check
    print(f"\n📋 Step 5: Final document check")
    try:
        response = requests.get(f"{base_url}/documents")
        if response.status_code == 200:
            result = response.json()
            summary = result.get('summary', {})
            print(f"   ✅ Final summary:")
            print(f"      Total documents: {summary.get('total', 0)}")
            print(f"      Images: {summary.get('images', 0)}")
            print(f"      PDFs: {summary.get('pdfs', 0)}")
    except Exception as e:
        print(f"   ❌ Error in final check: {e}")
    
    # Cleanup
    print(f"\n🧹 Cleanup")
    for path in image_paths:
        try:
            os.unlink(path)
            print(f"   🗑️ Deleted: {os.path.basename(path)}")
        except:
            pass
    
    print(f"\n🎉 Test completed!")

if __name__ == "__main__":
    test_complete_workflow()
