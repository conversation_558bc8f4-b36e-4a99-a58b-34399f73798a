"""
<PERSON><PERSON><PERSON> to create test user for manual testing
"""

import asyncio
from sqlalchemy.orm import Session
from app.database import SessionLocal, engine
from app.models.user import User
from app.models.document import Document
from app.database import Base

def create_test_user():
    """Create a test user for manual testing."""
    
    # Create tables if they don't exist
    Base.metadata.create_all(bind=engine)
    
    db = SessionLocal()
    try:
        # Check if test user already exists
        existing_user = db.query(User).filter(User.email == "<EMAIL>").first()
        
        if existing_user:
            print("✅ Test user already exists!")
            print(f"📧 Email: <EMAIL>")
            print(f"🔑 Password: testpassword123")
            print(f"👤 Username: testuser")
            print(f"🆔 User ID: {existing_user.id}")
            return existing_user
        
        # Create new test user
        test_user = User(
            email="<EMAIL>",
            username="testuser",
            hashed_password=User.get_password_hash("testpassword123"),
            is_active=True
        )
        
        db.add(test_user)
        db.commit()
        db.refresh(test_user)
        
        print("🎉 Test user created successfully!")
        print(f"📧 Email: <EMAIL>")
        print(f"🔑 Password: testpassword123")
        print(f"👤 Username: testuser")
        print(f"🆔 User ID: {test_user.id}")
        
        return test_user
        
    except Exception as e:
        print(f"❌ Error creating test user: {e}")
        db.rollback()
        return None
    finally:
        db.close()

def create_additional_test_users():
    """Create additional test users for comprehensive testing."""
    
    db = SessionLocal()
    try:
        test_users = [
            {
                "email": "<EMAIL>",
                "username": "admin",
                "password": "admin123"
            },
            {
                "email": "<EMAIL>", 
                "username": "demo",
                "password": "demo123"
            }
        ]
        
        created_users = []
        
        for user_data in test_users:
            # Check if user exists
            existing = db.query(User).filter(User.email == user_data["email"]).first()
            
            if not existing:
                new_user = User(
                    email=user_data["email"],
                    username=user_data["username"],
                    hashed_password=User.get_password_hash(user_data["password"]),
                    is_active=True
                )
                
                db.add(new_user)
                created_users.append(user_data)
        
        if created_users:
            db.commit()
            print(f"\n✅ Created {len(created_users)} additional test users:")
            for user in created_users:
                print(f"   📧 {user['email']} | 🔑 {user['password']}")
        else:
            print("\n✅ All additional test users already exist")
            
    except Exception as e:
        print(f"❌ Error creating additional users: {e}")
        db.rollback()
    finally:
        db.close()

if __name__ == "__main__":
    print("🚀 Creating test users for manual testing...\n")
    
    # Create main test user
    main_user = create_test_user()
    
    # Create additional test users
    create_additional_test_users()
    
    print("\n" + "="*50)
    print("🧪 TEST CREDENTIALS FOR MANUAL TESTING")
    print("="*50)
    print("Primary Test User:")
    print("📧 Email: <EMAIL>")
    print("🔑 Password: testpassword123")
    print("👤 Username: testuser")
    print("\nAdditional Test Users:")
    print("📧 Email: <EMAIL> | 🔑 Password: admin123")
    print("📧 Email: <EMAIL> | 🔑 Password: demo123")
    print("="*50)
