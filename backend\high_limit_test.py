"""
Test higher image limits
"""

import requests
from PIL import Image
import tempfile
import os
import time

def create_test_images(count):
    """Create test images."""
    images = []
    colors = ['red', 'green', 'blue', 'yellow', 'purple', 'orange', 'pink', 'cyan']
    
    print(f"📸 Creating {count} test images...")
    
    for i in range(count):
        tmp_file = tempfile.NamedTemporaryFile(suffix=f'_test_{i}.png', delete=False)
        tmp_file.close()
        
        color = colors[i % len(colors)]
        img = Image.new('RGB', (400, 300), color=color)
        img.save(tmp_file.name)
        
        images.append({
            'path': tmp_file.name,
            'name': f'test_{i+1}.png'
        })
        
        if (i + 1) % 100 == 0:
            print(f"   ✅ Created {i+1}/{count} images")
    
    return images

def test_high_count(count):
    """Test processing a high number of images."""
    print(f"\n🧪 Testing {count} images")
    
    base_url = "http://localhost:8001"
    
    # Create test images
    start_time = time.time()
    test_images = create_test_images(count)
    creation_time = time.time() - start_time
    
    # Calculate total size
    total_size = sum(os.path.getsize(img['path']) for img in test_images)
    print(f"   📁 Total size: {total_size / 1024 / 1024:.1f} MB")
    print(f"   ⏱️ Creation time: {creation_time:.1f}s")
    
    try:
        # Prepare files
        print(f"   📤 Preparing upload...")
        prep_start = time.time()
        files = []
        for img in test_images:
            with open(img['path'], 'rb') as f:
                files.append(('files', (img['name'], f.read(), 'image/png')))
        prep_time = time.time() - prep_start
        print(f"   ⏱️ Prep time: {prep_time:.1f}s")
        
        data = {
            'generate_pdf': 'true',
            'pdf_name': f'High_Test_{count}_Images'
        }
        
        # Send request with longer timeout
        print(f"   🚀 Sending request...")
        upload_start = time.time()
        response = requests.post(f"{base_url}/api/v1/documents/upload-multiple", 
                               files=files, data=data, timeout=600)  # 10 minute timeout
        upload_time = time.time() - upload_start
        
        if response.status_code == 200:
            result = response.json()
            pdf_info = result.get('pdf_result', {})
            
            print(f"   ✅ SUCCESS!")
            print(f"   📄 Processed: {result['processed_count']} images")
            print(f"   📄 PDF size: {pdf_info.get('pdf_size', 0) / 1024 / 1024:.1f} MB")
            print(f"   ⏱️ Upload time: {upload_time:.1f}s")
            print(f"   ⏱️ Total time: {creation_time + prep_time + upload_time:.1f}s")
            return True
        else:
            print(f"   ❌ FAILED: {response.status_code}")
            print(f"   Error: {response.text[:200]}...")
            return False
            
    except Exception as e:
        print(f"   ❌ ERROR: {e}")
        return False
    
    finally:
        # Cleanup
        print(f"   🧹 Cleaning up...")
        for img in test_images:
            try:
                os.unlink(img['path'])
            except:
                pass

def high_limit_test():
    """Test high image counts."""
    print("🚀 HIGH LIMIT TEST")
    print("=" * 30)
    
    # Test high counts
    test_counts = [500, 750, 1000, 1500, 2000]
    max_successful = 300  # From previous test
    
    for count in test_counts:
        success = test_high_count(count)
        if success:
            max_successful = count
            print(f"✅ {count} images: SUCCESS")
        else:
            print(f"❌ {count} images: FAILED")
            break
    
    return max_successful

if __name__ == "__main__":
    max_images = high_limit_test()
    print(f"\n🎯 MAXIMUM SUCCESSFUL: {max_images} images")
