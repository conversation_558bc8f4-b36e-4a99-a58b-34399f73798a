# 🚀 Quick Start Manual Testing Guide

## 🔐 Test Credentials

### Primary Test Account
```
📧 Email: <EMAIL>
🔑 Password: testpassword123
👤 Username: testuser
```

### Alternative Test Accounts
```
📧 Email: <EMAIL>
🔑 Password: admin123

📧 Email: <EMAIL>  
🔑 Password: demo123
```

## ⚡ Quick Setup (5 Minutes)

### Step 1: Install Dependencies
```bash
# Backend dependencies
cd backend
pip install fastapi uvicorn python-multipart sqlalchemy psycopg2-binary redis celery passlib[bcrypt] python-jose[cryptography] pillow reportlab

# If you have Node.js for frontend
cd ../frontend
npm install
```

### Step 2: Start Services

#### Option A: Backend Only (API Testing)
```bash
cd backend
python -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```
**Access**: http://localhost:8000/docs (Swagger UI)

#### Option B: Full Stack (if Node.js available)
```bash
# Terminal 1: Backend
cd backend
python -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8000

# Terminal 2: Frontend  
cd frontend
npm start
```
**Access**: http://localhost:3000

### Step 3: Create Test User (Optional)
```bash
cd backend
python create_test_user.py
```

## 🧪 Quick Test Scenarios

### Test 1: API Testing (Backend Only)
1. Go to http://localhost:8000/docs
2. Click "Authorize" and login with test credentials
3. Test `/documents/upload-multiple` endpoint
4. Upload multiple image files
5. Test `/documents/images-to-pdf` endpoint

### Test 2: Full Application Testing
1. Go to http://localhost:3000
2. Login with test credentials
3. Navigate to Documents page
4. Test multiple image upload with PDF generation

## 📋 Core Test Cases

### ✅ Test Case 1: Multiple Image Upload
**What to test**: Upload 3-5 images at once
**Expected**: All images uploaded, option to generate PDF appears

### ✅ Test Case 2: PDF Generation During Upload
**What to test**: Select "Generate PDF" option during upload
**Expected**: Images uploaded + PDF created automatically

### ✅ Test Case 3: Convert Existing Images to PDF
**What to test**: Use "Convert Images to PDF" tab
**Expected**: Select images → Configure PDF → Generate successfully

### ✅ Test Case 4: Different PDF Configurations
**What to test**: Try different orientations and fit modes
**Expected**: PDFs generated with different layouts

### ✅ Test Case 5: File Validation
**What to test**: Upload invalid files (txt, exe, oversized)
**Expected**: Clear error messages, files rejected

## 🎯 Success Criteria

The testing is successful if:
- ✅ Multiple images can be uploaded simultaneously
- ✅ PDF generation works during upload
- ✅ Existing images can be converted to PDF
- ✅ Different PDF configurations work
- ✅ File validation prevents invalid uploads
- ✅ Error messages are clear and helpful
- ✅ User interface is intuitive

## 🔧 Troubleshooting

### Common Issues:

**"Module not found" errors**:
```bash
pip install [missing-module]
```

**Port already in use**:
```bash
# Change port
python -m uvicorn app.main:app --reload --port 8001
```

**Database errors**:
```bash
# The app uses SQLite by default, no setup needed
# Check if uploads/ directory exists
mkdir uploads
```

**CORS errors**:
- Ensure backend is running on port 8000
- Check CORS settings in app/main.py

## 📞 Quick Support

If you encounter issues:
1. Check if backend is running: http://localhost:8000/docs
2. Check browser console for errors (F12)
3. Verify test credentials are correct
4. Ensure file types are supported (PNG, JPG, PDF)
5. Check file sizes are under 10MB

## 🎉 Ready to Test!

You now have everything needed to manually test the multiple image upload and PDF generation functionality. The system supports:

- **Multiple file upload** with drag-and-drop
- **Automatic PDF generation** from multiple images  
- **Flexible PDF configuration** (orientation, fit modes)
- **Batch processing** with progress tracking
- **File validation** and error handling
- **Professional PDF output** with proper image scaling

Start with the Quick Test Scenarios above and work through the Core Test Cases to verify all functionality is working correctly!
