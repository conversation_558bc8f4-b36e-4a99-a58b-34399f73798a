"""
Multiple Image Upload & PDF Generation Demo
Complete implementation of multiple image upload with automatic PDF generation
"""

from fastapi import FastAPI, UploadFile, File, Form, HTTPException, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import FileResponse, HTMLResponse
from typing import List, Optional
import os
import uuid
from PIL import Image
from reportlab.pdfgen import canvas
from reportlab.lib.pagesizes import letter, landscape
from sqlalchemy import create_engine, Column, Integer, String, DateTime, Text
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, Session
from datetime import datetime
import tempfile

# Database setup (using SQLite for simplicity)
SQLALCHEMY_DATABASE_URL = "sqlite:///./documents.db"
engine = create_engine(SQLALCHEMY_DATABASE_URL, connect_args={"check_same_thread": False})
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
Base = declarative_base()

# Document model
class Document(Base):
    __tablename__ = "documents"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, index=True)
    file_path = Column(String)
    mime_type = Column(String)
    size = Column(Integer)
    status = Column(String, default="completed")
    created_at = Column(DateTime, default=datetime.utcnow)

# Create tables
Base.metadata.create_all(bind=engine)

# FastAPI app
app = FastAPI(
    title="Multiple Image Upload & PDF Generation Demo",
    description="Upload multiple images and generate PDF documents",
    version="1.0.0"
)

# Add CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Create uploads directory
os.makedirs("uploads", exist_ok=True)

# Dependency to get database session
def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

# Helper functions

def create_pdf_from_images(image_paths: List[str], pdf_name: str, orientation: str = "portrait") -> str:
    """Create PDF from multiple images."""
    pagesize = landscape(letter) if orientation == "landscape" else letter
    
    pdf_filename = f"{pdf_name}_{uuid.uuid4().hex[:8]}.pdf"
    pdf_path = os.path.join("uploads", pdf_filename)
    
    c = canvas.Canvas(pdf_path, pagesize=pagesize)
    page_width, page_height = pagesize
    
    for img_path in image_paths:
        try:
            # Get image dimensions
            with Image.open(img_path) as img:
                img_width, img_height = img.size
            
            # Calculate scaling to fit page
            margin = 50
            available_width = page_width - 2 * margin
            available_height = page_height - 2 * margin
            
            scale_x = available_width / img_width
            scale_y = available_height / img_height
            scale = min(scale_x, scale_y)
            
            new_width = img_width * scale
            new_height = img_height * scale
            
            # Center the image
            x = margin + (available_width - new_width) / 2
            y = margin + (available_height - new_height) / 2
            
            # Add image to PDF
            c.drawImage(img_path, x, y, width=new_width, height=new_height)
            c.showPage()
            
        except Exception as e:
            print(f"Error processing image {img_path}: {e}")
            continue
    
    c.save()
    return pdf_path

# API Endpoints
@app.get("/", response_class=HTMLResponse)
def root():
    """Main interface - Multiple Image Upload & PDF Generation."""
    html_content = """
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Multiple Image Upload & PDF Generation</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        input[type="file"] {
            width: 100%;
            padding: 10px;
            border: 2px dashed #ddd;
            border-radius: 5px;
            background: #fafafa;
        }
        input[type="text"] {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            box-sizing: border-box;
        }
        .checkbox-group {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        button {
            background: #007bff;
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            width: 100%;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            display: none;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .file-info {
            margin-top: 10px;
            padding: 10px;
            background: #e9ecef;
            border-radius: 5px;
            font-size: 14px;
        }
        .tip {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📚 Multiple Image to PDF Converter</h1>

        <div class="tip">
            <strong>💡 TIP:</strong> Hold <kbd>Ctrl</kbd> (Windows) or <kbd>Cmd</kbd> (Mac) while clicking to select multiple images at once! Images are processed in memory and only the PDF is saved.
        </div>

        <form id="uploadForm">
            <div class="form-group">
                <label for="files">Select Multiple Images:</label>
                <input type="file" id="files" name="files" multiple accept="image/*" required>
                <div id="fileInfo" class="file-info" style="display: none;"></div>
            </div>

            <div class="form-group">
                <label for="pdfName">PDF Name:</label>
                <input type="text" id="pdfName" name="pdf_name" value="Combined_Images_PDF" placeholder="Enter PDF name" required>
            </div>

            <button type="submit" id="submitBtn">Convert Images to PDF</button>
        </form>

        <div id="result" class="result"></div>
    </div>

    <script>
        const form = document.getElementById('uploadForm');
        const filesInput = document.getElementById('files');
        const fileInfo = document.getElementById('fileInfo');
        const result = document.getElementById('result');
        const submitBtn = document.getElementById('submitBtn');

        // Show file information when files are selected
        filesInput.addEventListener('change', function() {
            const files = this.files;
            if (files.length > 0) {
                let info = `Selected ${files.length} file(s):\\n`;
                for (let i = 0; i < files.length; i++) {
                    const file = files[i];
                    const sizeKB = (file.size / 1024).toFixed(1);
                    info += `• ${file.name} (${sizeKB} KB)\\n`;
                }
                fileInfo.textContent = info;
                fileInfo.style.display = 'block';
            } else {
                fileInfo.style.display = 'none';
            }
        });

        // Handle form submission
        form.addEventListener('submit', async function(e) {
            e.preventDefault();

            const files = filesInput.files;
            if (files.length === 0) {
                showResult('Please select at least one image file.', 'error');
                return;
            }

            // Disable submit button
            submitBtn.disabled = true;
            submitBtn.textContent = 'Converting...';

            // Prepare form data
            const formData = new FormData();

            // Add all files
            for (let i = 0; i < files.length; i++) {
                formData.append('files', files[i]);
            }

            // Add other form data
            formData.append('generate_pdf', 'true');
            formData.append('pdf_name', document.getElementById('pdfName').value || 'Combined_Images_PDF');

            try {
                // Send request
                const response = await fetch('/api/v1/documents/upload-multiple', {
                    method: 'POST',
                    body: formData
                });

                const data = await response.json();

                if (response.ok) {
                    let message = `✅ Success! Processed ${data.processed_count} images.\\n`;
                    message += `Message: ${data.message}\\n`;

                    if (data.pdf_result && data.pdf_result.status === 'generated') {
                        message += `\\n📄 PDF Generated:\\n`;
                        message += `• Name: ${data.pdf_result.pdf_name}\\n`;
                        message += `• Size: ${data.pdf_result.pdf_size} bytes\\n`;
                        message += `• Pages: ${data.pdf_result.pages}\\n`;
                        message += `• Download: ${data.pdf_result.download_url}`;
                    }

                    showResult(message, 'success');
                } else {
                    showResult(`❌ Error: ${data.detail || 'Processing failed'}`, 'error');
                }
            } catch (error) {
                showResult(`❌ Network Error: ${error.message}`, 'error');
            } finally {
                // Re-enable submit button
                submitBtn.disabled = false;
                submitBtn.textContent = 'Convert Images to PDF';
            }
        });

        function showResult(message, type) {
            result.textContent = message;
            result.className = `result ${type}`;
            result.style.display = 'block';

            // Scroll to result
            result.scrollIntoView({ behavior: 'smooth' });
        }
    </script>
</body>
</html>
    """
    return HTMLResponse(content=html_content)

@app.post("/api/v1/documents/upload-multiple",
          summary="Upload Multiple Images & Generate PDF",
          description="Upload multiple images at once and optionally generate a combined PDF document")
async def upload_multiple_documents(
    files: List[UploadFile] = File(..., description="Multiple image files (PNG, JPG, JPEG)"),
    generate_pdf: bool = Form(True, description="Generate PDF from uploaded images"),
    pdf_name: Optional[str] = Form("Combined_Images", description="Name for the generated PDF"),
    db: Session = Depends(get_db)
):
    """Process multiple images and generate PDF - images are not saved individually."""

    if not files:
        raise HTTPException(status_code=400, detail="No files provided")

    if not generate_pdf:
        raise HTTPException(status_code=400, detail="PDF generation is required - images are not saved individually")

    processed_files = []
    temp_image_paths = []

    # Process all files in memory and create temporary files for PDF generation
    for file in files:
        try:
            # Validate file type
            if not file.content_type or not file.content_type.startswith('image/'):
                processed_files.append({
                    "name": file.filename,
                    "status": "failed",
                    "message": "Invalid file type - only images allowed"
                })
                continue

            # Read file content
            content = await file.read()
            file_size = len(content)

            # Validate image by opening it
            try:
                from io import BytesIO
                img = Image.open(BytesIO(content))
                img.verify()  # Verify it's a valid image
            except Exception:
                processed_files.append({
                    "name": file.filename,
                    "status": "failed",
                    "message": "Invalid or corrupted image file"
                })
                continue

            # Create temporary file for PDF generation
            import tempfile
            temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.png')
            temp_file.write(content)
            temp_file.close()

            temp_image_paths.append(temp_file.name)

            processed_files.append({
                "name": file.filename,
                "status": "processed",
                "size": file_size,
                "message": "Image processed for PDF generation"
            })

        except Exception as e:
            processed_files.append({
                "name": file.filename or "unknown",
                "status": "failed",
                "message": f"Processing failed: {str(e)}"
            })

    pdf_result = None

    # Generate PDF from processed images
    if temp_image_paths and pdf_name:
        try:
            pdf_path = create_pdf_from_images(temp_image_paths, pdf_name)
            pdf_size = os.path.getsize(pdf_path)
            pdf_filename = os.path.basename(pdf_path)

            # Save only PDF to database
            pdf_document = Document(
                name=pdf_filename,
                file_path=pdf_path,
                mime_type="application/pdf",
                size=pdf_size,
                status="completed"
            )
            db.add(pdf_document)
            db.commit()
            db.refresh(pdf_document)

            pdf_result = {
                "pdf_id": pdf_document.id,
                "pdf_name": pdf_filename,
                "pdf_size": pdf_size,
                "pages": len(temp_image_paths),
                "status": "generated",
                "download_url": f"/download/{pdf_filename}"
            }

        except Exception as e:
            pdf_result = {
                "status": "failed",
                "error": str(e)
            }
        finally:
            # Clean up temporary files
            for temp_path in temp_image_paths:
                try:
                    os.unlink(temp_path)
                except:
                    pass

    successful_count = len([f for f in processed_files if f["status"] == "processed"])

    return {
        "processed_files": processed_files,
        "processed_count": successful_count,
        "pdf_result": pdf_result,
        "message": f"✅ Processed {successful_count} images" + (
            f", PDF generated!" if pdf_result and pdf_result.get("status") == "generated" else ""
        )
    }

# Documents list endpoint for frontend
@app.get("/api/v1/documents",
         summary="📋 List Documents",
         description="Get list of all documents with summary statistics")
def list_documents():
    """Get list of all documents."""
    try:
        uploads_dir = "uploads"
        if not os.path.exists(uploads_dir):
            os.makedirs(uploads_dir)

        documents = []
        total_size = 0
        pdf_count = 0
        image_count = 0

        # Get all files in uploads directory
        for filename in os.listdir(uploads_dir):
            file_path = os.path.join(uploads_dir, filename)
            if os.path.isfile(file_path):
                file_size = os.path.getsize(file_path)
                file_ext = filename.lower().split('.')[-1]

                # Determine file type
                if file_ext == 'pdf':
                    file_type = 'pdf'
                    pdf_count += 1
                elif file_ext in ['png', 'jpg', 'jpeg']:
                    file_type = 'image'
                    image_count += 1
                else:
                    continue  # Skip unknown file types

                # Get file creation time
                created_at = datetime.fromtimestamp(os.path.getctime(file_path)).isoformat()

                documents.append({
                    'id': hash(filename) % 1000000,  # Simple ID generation
                    'name': filename,
                    'size': file_size,
                    'created_at': created_at,
                    'type': file_type
                })

                total_size += file_size

        # Sort by creation time (newest first)
        documents.sort(key=lambda x: x['created_at'], reverse=True)

        return {
            'documents': documents,
            'summary': {
                'total': len(documents),
                'pdfs': pdf_count,
                'images': image_count,
                'total_size': total_size
            }
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error listing documents: {str(e)}")

@app.get("/download/{filename}",
         summary="📥 Download File - Main Project",
         description="Download a generated PDF or uploaded image file.")
def download_file(filename: str):
    """Download a file."""
    file_path = os.path.join("uploads", filename)
    if os.path.exists(file_path):
        return FileResponse(file_path, filename=filename)
    else:
        raise HTTPException(status_code=404, detail="File not found")

if __name__ == "__main__":
    import uvicorn
    print("🚀 Starting Multiple Image to PDF Converter")
    print("=" * 60)
    print("🎯 FEATURES:")
    print("   📚 Process multiple images in memory")
    print("   📄 Generate PDF from images")
    print("   💾 Save only PDF files (images not stored)")
    print("   📥 Download generated PDFs")
    print("=" * 60)
    print("🌐 Open: http://localhost:8001")
    print("💡 TIP: Hold Ctrl/Cmd to select multiple images!")

    uvicorn.run(app, host="0.0.0.0", port=8001)
