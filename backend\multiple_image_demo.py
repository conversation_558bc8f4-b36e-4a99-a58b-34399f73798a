"""
Multiple Image to PDF Converter - Production Ready Application
A robust, secure, and scalable image processing service with comprehensive error handling,
logging, validation, and performance optimizations.

Author: Document Processing Platform Team
Version: 2.0.0
License: MIT
"""

import os
import uuid
import tempfile
import logging
import mimetypes
from datetime import datetime
from typing import List, Optional, Dict, Any
from pathlib import Path
from contextlib import asynccontextmanager

# FastAPI and Web Framework
from fastapi import FastAPI, UploadFile, File, Form, HTTPException, Depends, Request, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import FileResponse, HTMLResponse, JSONResponse
from fastapi.security import HTTPBearer
from fastapi.staticfiles import StaticFiles

# Database and ORM
from sqlalchemy import create_engine, Column, Integer, String, DateTime, Text, Boolean
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, Session

# Image and PDF Processing
from PIL import Image, ImageOps
from reportlab.pdfgen import canvas
from reportlab.lib.pagesizes import letter, landscape, A4
from reportlab.lib.utils import ImageReader

# Configuration and Environment
from pydantic import BaseSettings, validator
from pydantic_settings import BaseSettings as PydanticBaseSettings

# Utilities
import asyncio
from io import BytesIO

# ============================================================================
# CONFIGURATION AND SETTINGS
# ============================================================================

class Settings(PydanticBaseSettings):
    """Application configuration settings."""

    # Application
    app_name: str = "Multiple Image to PDF Converter"
    app_version: str = "2.0.0"
    debug: bool = False

    # Server
    host: str = "0.0.0.0"
    port: int = 8001

    # Database
    database_url: str = "sqlite:///./documents.db"

    # File Processing
    max_file_size: int = 10 * 1024 * 1024  # 10MB
    max_files_per_request: int = 50
    allowed_extensions: List[str] = [".png", ".jpg", ".jpeg", ".webp"]
    upload_dir: str = "uploads"

    # Security
    cors_origins: List[str] = ["http://localhost:3000", "http://localhost:8001"]
    trusted_hosts: List[str] = ["localhost", "127.0.0.1"]

    # PDF Generation
    pdf_page_size: str = "letter"  # letter, a4, legal
    pdf_orientation: str = "portrait"  # portrait, landscape
    pdf_quality: int = 85

    class Config:
        env_file = ".env"
        case_sensitive = False

# Initialize settings
settings = Settings()

# ============================================================================
# LOGGING CONFIGURATION
# ============================================================================

def setup_logging():
    """Configure application logging."""
    log_level = logging.DEBUG if settings.debug else logging.INFO

    logging.basicConfig(
        level=log_level,
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler("app.log", mode="a")
        ]
    )

    # Reduce noise from external libraries
    logging.getLogger("uvicorn.access").setLevel(logging.WARNING)
    logging.getLogger("PIL").setLevel(logging.WARNING)

    return logging.getLogger(__name__)

logger = setup_logging()

# ============================================================================
# DATABASE SETUP
# ============================================================================

engine = create_engine(
    settings.database_url,
    connect_args={"check_same_thread": False} if "sqlite" in settings.database_url else {}
)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
Base = declarative_base()

class Document(Base):
    """Database model for document storage."""
    __tablename__ = "documents"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, index=True, nullable=False)
    file_path = Column(String, nullable=False)
    mime_type = Column(String, nullable=False)
    size = Column(Integer, nullable=False)
    status = Column(String, default="completed", nullable=False)
    created_at = Column(DateTime, default=datetime.now, nullable=False)

    def __repr__(self):
        return f"<Document(id={self.id}, name='{self.name}', size={self.size})>"

# Create tables
Base.metadata.create_all(bind=engine)
logger.info("Database tables created successfully")

# ============================================================================
# UTILITY FUNCTIONS AND VALIDATORS
# ============================================================================

def validate_file_type(file: UploadFile) -> bool:
    """Validate if file type is allowed."""
    if not file.filename:
        return False

    file_ext = Path(file.filename).suffix.lower()
    return file_ext in settings.allowed_extensions

def validate_file_size(content: bytes) -> bool:
    """Validate if file size is within limits."""
    return len(content) <= settings.max_file_size

def sanitize_filename(filename: str) -> str:
    """Sanitize filename to prevent path traversal attacks."""
    # Remove path components and dangerous characters
    safe_filename = Path(filename).name
    safe_filename = "".join(c for c in safe_filename if c.isalnum() or c in "._-")

    # Ensure filename is not empty and has reasonable length
    if not safe_filename or len(safe_filename) > 255:
        safe_filename = f"file_{uuid.uuid4().hex[:8]}"

    return safe_filename

def get_file_mime_type(filename: str) -> str:
    """Get MIME type for file."""
    mime_type, _ = mimetypes.guess_type(filename)
    return mime_type or "application/octet-stream"

async def validate_image_content(content: bytes) -> bool:
    """Validate that content is actually a valid image."""
    try:
        with Image.open(BytesIO(content)) as img:
            img.verify()
        return True
    except Exception as e:
        logger.warning(f"Image validation failed: {e}")
        return False

# ============================================================================
# FASTAPI APPLICATION SETUP
# ============================================================================

app = FastAPI(
    title=settings.app_name,
    description="A robust, secure, and scalable image processing service",
    version=settings.app_version,
    docs_url="/docs" if settings.debug else None,
    redoc_url="/redoc" if settings.debug else None,
)

# Security Middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.cors_origins,
    allow_credentials=True,
    allow_methods=["GET", "POST", "OPTIONS"],
    allow_headers=["*"],
)

# Ensure uploads directory exists
upload_path = Path(settings.upload_dir)
upload_path.mkdir(exist_ok=True)
logger.info(f"Upload directory ensured: {upload_path.absolute()}")

# Dependency to get database session
def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

# Helper functions

# ============================================================================
# PDF GENERATION FUNCTIONS
# ============================================================================

def get_page_size(size_name: str):
    """Get page size from name."""
    sizes = {
        "letter": letter,
        "a4": A4,
        "legal": (612, 1008)  # Legal size
    }
    return sizes.get(size_name.lower(), letter)

def optimize_image_for_pdf(image_path: str, max_dimension: int = 2048) -> str:
    """Optimize image for PDF generation to reduce file size."""
    try:
        with Image.open(image_path) as img:
            # Convert to RGB if necessary
            if img.mode in ('RGBA', 'LA', 'P'):
                img = img.convert('RGB')

            # Resize if too large
            if max(img.size) > max_dimension:
                img.thumbnail((max_dimension, max_dimension), Image.Resampling.LANCZOS)

            # Save optimized version
            optimized_path = f"{image_path}_optimized.jpg"
            img.save(optimized_path, "JPEG", quality=settings.pdf_quality, optimize=True)
            return optimized_path

    except Exception as e:
        logger.error(f"Error optimizing image {image_path}: {e}")
        return image_path  # Return original if optimization fails

def create_pdf_from_images(image_paths: List[str], pdf_name: str, orientation: str = None) -> str:
    """
    Create PDF from multiple images with improved error handling and optimization.

    Args:
        image_paths: List of image file paths
        pdf_name: Name for the PDF file
        orientation: Page orientation (portrait/landscape)

    Returns:
        Path to the created PDF file

    Raises:
        Exception: If PDF creation fails
    """
    if not image_paths:
        raise ValueError("No images provided for PDF generation")

    # Use settings for orientation if not specified
    orientation = orientation or settings.pdf_orientation

    # Get page size and orientation
    base_pagesize = get_page_size(settings.pdf_page_size)
    pagesize = landscape(base_pagesize) if orientation == "landscape" else base_pagesize

    # Generate safe filename
    safe_pdf_name = sanitize_filename(pdf_name)
    pdf_filename = f"{safe_pdf_name}_{uuid.uuid4().hex[:8]}.pdf"
    pdf_path = Path(settings.upload_dir) / pdf_filename

    logger.info(f"Creating PDF: {pdf_filename} with {len(image_paths)} images")

    try:
        c = canvas.Canvas(str(pdf_path), pagesize=pagesize)
        page_width, page_height = pagesize

        processed_images = 0
        optimized_files = []  # Track optimized files for cleanup

        for i, img_path in enumerate(image_paths):
            try:
                logger.debug(f"Processing image {i+1}/{len(image_paths)}: {img_path}")

                # Optimize image for PDF
                optimized_path = optimize_image_for_pdf(img_path)
                if optimized_path != img_path:
                    optimized_files.append(optimized_path)

                # Get image dimensions
                with Image.open(optimized_path) as img:
                    img_width, img_height = img.size

                # Calculate scaling to fit page with margins
                margin = 50
                available_width = page_width - 2 * margin
                available_height = page_height - 2 * margin

                scale_x = available_width / img_width
                scale_y = available_height / img_height
                scale = min(scale_x, scale_y, 1.0)  # Don't upscale

                new_width = img_width * scale
                new_height = img_height * scale

                # Center the image
                x = margin + (available_width - new_width) / 2
                y = margin + (available_height - new_height) / 2

                # Add image to PDF
                c.drawImage(optimized_path, x, y, width=new_width, height=new_height)
                c.showPage()

                processed_images += 1

            except Exception as e:
                logger.error(f"Error processing image {img_path}: {e}")
                # Continue with other images instead of failing completely
                continue

        if processed_images == 0:
            raise Exception("No images could be processed successfully")

        c.save()

        # Cleanup optimized files
        for opt_file in optimized_files:
            try:
                os.unlink(opt_file)
            except:
                pass

        logger.info(f"PDF created successfully: {pdf_filename} ({processed_images} pages)")
        return str(pdf_path)

    except Exception as e:
        # Cleanup on failure
        if pdf_path.exists():
            pdf_path.unlink()
        logger.error(f"PDF creation failed: {e}")
        raise Exception(f"Failed to create PDF: {str(e)}")

# API Endpoints
@app.get("/", response_class=HTMLResponse)
def root():
    """Main interface - Multiple Image Upload & PDF Generation."""
    html_content = """
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Multiple Image Upload & PDF Generation</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        input[type="file"] {
            width: 100%;
            padding: 10px;
            border: 2px dashed #ddd;
            border-radius: 5px;
            background: #fafafa;
        }
        input[type="text"] {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            box-sizing: border-box;
        }
        .checkbox-group {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        button {
            background: #007bff;
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            width: 100%;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            display: none;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .file-info {
            margin-top: 10px;
            padding: 10px;
            background: #e9ecef;
            border-radius: 5px;
            font-size: 14px;
        }
        .tip {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📚 Multiple Image to PDF Converter</h1>

        <div class="tip">
            <strong>💡 TIP:</strong> Hold <kbd>Ctrl</kbd> (Windows) or <kbd>Cmd</kbd> (Mac) while clicking to select multiple images at once! Images are processed in memory and only the PDF is saved.
        </div>

        <form id="uploadForm">
            <div class="form-group">
                <label for="files">Select Multiple Images:</label>
                <input type="file" id="files" name="files" multiple accept="image/*" required>
                <div id="fileInfo" class="file-info" style="display: none;"></div>
            </div>

            <div class="form-group">
                <label for="pdfName">PDF Name:</label>
                <input type="text" id="pdfName" name="pdf_name" value="Combined_Images_PDF" placeholder="Enter PDF name" required>
            </div>

            <button type="submit" id="submitBtn">Convert Images to PDF</button>
        </form>

        <div id="result" class="result"></div>
    </div>

    <script>
        const form = document.getElementById('uploadForm');
        const filesInput = document.getElementById('files');
        const fileInfo = document.getElementById('fileInfo');
        const result = document.getElementById('result');
        const submitBtn = document.getElementById('submitBtn');

        // Show file information when files are selected
        filesInput.addEventListener('change', function() {
            const files = this.files;
            if (files.length > 0) {
                let info = `Selected ${files.length} file(s):\\n`;
                for (let i = 0; i < files.length; i++) {
                    const file = files[i];
                    const sizeKB = (file.size / 1024).toFixed(1);
                    info += `• ${file.name} (${sizeKB} KB)\\n`;
                }
                fileInfo.textContent = info;
                fileInfo.style.display = 'block';
            } else {
                fileInfo.style.display = 'none';
            }
        });

        // Handle form submission
        form.addEventListener('submit', async function(e) {
            e.preventDefault();

            const files = filesInput.files;
            if (files.length === 0) {
                showResult('Please select at least one image file.', 'error');
                return;
            }

            // Disable submit button
            submitBtn.disabled = true;
            submitBtn.textContent = 'Converting...';

            // Prepare form data
            const formData = new FormData();

            // Add all files
            for (let i = 0; i < files.length; i++) {
                formData.append('files', files[i]);
            }

            // Add other form data
            formData.append('generate_pdf', 'true');
            formData.append('pdf_name', document.getElementById('pdfName').value || 'Combined_Images_PDF');

            try {
                // Send request
                const response = await fetch('/api/v1/documents/upload-multiple', {
                    method: 'POST',
                    body: formData
                });

                const data = await response.json();

                if (response.ok) {
                    let message = `✅ Success! Processed ${data.processed_count} images.\\n`;
                    message += `Message: ${data.message}\\n`;

                    if (data.pdf_result && data.pdf_result.status === 'generated') {
                        message += `\\n📄 PDF Generated:\\n`;
                        message += `• Name: ${data.pdf_result.pdf_name}\\n`;
                        message += `• Size: ${data.pdf_result.pdf_size} bytes\\n`;
                        message += `• Pages: ${data.pdf_result.pages}\\n`;
                        message += `• Download: ${data.pdf_result.download_url}`;
                    }

                    showResult(message, 'success');
                } else {
                    showResult(`❌ Error: ${data.detail || 'Processing failed'}`, 'error');
                }
            } catch (error) {
                showResult(`❌ Network Error: ${error.message}`, 'error');
            } finally {
                // Re-enable submit button
                submitBtn.disabled = false;
                submitBtn.textContent = 'Convert Images to PDF';
            }
        });

        function showResult(message, type) {
            result.textContent = message;
            result.className = `result ${type}`;
            result.style.display = 'block';

            // Scroll to result
            result.scrollIntoView({ behavior: 'smooth' });
        }
    </script>
</body>
</html>
    """
    return HTMLResponse(content=html_content)

@app.post("/api/v1/documents/upload-multiple",
          summary="Upload Multiple Images & Generate PDF",
          description="Upload multiple images at once and optionally generate a combined PDF document")
async def upload_multiple_documents(
    files: List[UploadFile] = File(..., description="Multiple image files (PNG, JPG, JPEG)"),
    generate_pdf: bool = Form(True, description="Generate PDF from uploaded images"),
    pdf_name: Optional[str] = Form("Combined_Images", description="Name for the generated PDF"),
    db: Session = Depends(get_db)
):
    """Process multiple images and generate PDF - images are not saved individually."""

    if not files:
        raise HTTPException(status_code=400, detail="No files provided")

    if not generate_pdf:
        raise HTTPException(status_code=400, detail="PDF generation is required - images are not saved individually")

    processed_files = []
    temp_image_paths = []

    # Process all files in memory and create temporary files for PDF generation
    for file in files:
        try:
            # Validate file type
            if not file.content_type or not file.content_type.startswith('image/'):
                processed_files.append({
                    "name": file.filename,
                    "status": "failed",
                    "message": "Invalid file type - only images allowed"
                })
                continue

            # Read file content
            content = await file.read()
            file_size = len(content)

            # Validate image by opening it
            try:
                from io import BytesIO
                img = Image.open(BytesIO(content))
                img.verify()  # Verify it's a valid image
            except Exception:
                processed_files.append({
                    "name": file.filename,
                    "status": "failed",
                    "message": "Invalid or corrupted image file"
                })
                continue

            # Create temporary file for PDF generation
            import tempfile
            temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.png')
            temp_file.write(content)
            temp_file.close()

            temp_image_paths.append(temp_file.name)

            processed_files.append({
                "name": file.filename,
                "status": "processed",
                "size": file_size,
                "message": "Image processed for PDF generation"
            })

        except Exception as e:
            processed_files.append({
                "name": file.filename or "unknown",
                "status": "failed",
                "message": f"Processing failed: {str(e)}"
            })

    pdf_result = None

    # Generate PDF from processed images
    if temp_image_paths and pdf_name:
        try:
            pdf_path = create_pdf_from_images(temp_image_paths, pdf_name)
            pdf_size = os.path.getsize(pdf_path)
            pdf_filename = os.path.basename(pdf_path)

            # Save only PDF to database
            pdf_document = Document(
                name=pdf_filename,
                file_path=pdf_path,
                mime_type="application/pdf",
                size=pdf_size,
                status="completed"
            )
            db.add(pdf_document)
            db.commit()
            db.refresh(pdf_document)

            pdf_result = {
                "pdf_id": pdf_document.id,
                "pdf_name": pdf_filename,
                "pdf_size": pdf_size,
                "pages": len(temp_image_paths),
                "status": "generated",
                "download_url": f"/download/{pdf_filename}"
            }

        except Exception as e:
            pdf_result = {
                "status": "failed",
                "error": str(e)
            }
        finally:
            # Clean up temporary files
            for temp_path in temp_image_paths:
                try:
                    os.unlink(temp_path)
                except:
                    pass

    successful_count = len([f for f in processed_files if f["status"] == "processed"])

    return {
        "processed_files": processed_files,
        "processed_count": successful_count,
        "pdf_result": pdf_result,
        "message": f"✅ Processed {successful_count} images" + (
            f", PDF generated!" if pdf_result and pdf_result.get("status") == "generated" else ""
        )
    }

# Documents list endpoint for frontend
@app.get("/api/v1/documents",
         summary="📋 List Documents",
         description="Get list of all documents with summary statistics")
def list_documents():
    """Get list of all documents."""
    try:
        uploads_dir = "uploads"
        if not os.path.exists(uploads_dir):
            os.makedirs(uploads_dir)

        documents = []
        total_size = 0
        pdf_count = 0
        image_count = 0

        # Get all files in uploads directory
        for filename in os.listdir(uploads_dir):
            file_path = os.path.join(uploads_dir, filename)
            if os.path.isfile(file_path):
                file_size = os.path.getsize(file_path)
                file_ext = filename.lower().split('.')[-1]

                # Determine file type
                if file_ext == 'pdf':
                    file_type = 'pdf'
                    pdf_count += 1
                elif file_ext in ['png', 'jpg', 'jpeg']:
                    file_type = 'image'
                    image_count += 1
                else:
                    continue  # Skip unknown file types

                # Get file creation time
                created_at = datetime.fromtimestamp(os.path.getctime(file_path)).isoformat()

                documents.append({
                    'id': hash(filename) % 1000000,  # Simple ID generation
                    'name': filename,
                    'size': file_size,
                    'created_at': created_at,
                    'type': file_type
                })

                total_size += file_size

        # Sort by creation time (newest first)
        documents.sort(key=lambda x: x['created_at'], reverse=True)

        return {
            'documents': documents,
            'summary': {
                'total': len(documents),
                'pdfs': pdf_count,
                'images': image_count,
                'total_size': total_size
            }
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error listing documents: {str(e)}")

@app.get("/download/{filename}",
         summary="📥 Download File - Main Project",
         description="Download a generated PDF or uploaded image file.")
def download_file(filename: str):
    """Download a file."""
    file_path = os.path.join("uploads", filename)
    if os.path.exists(file_path):
        return FileResponse(file_path, filename=filename)
    else:
        raise HTTPException(status_code=404, detail="File not found")

if __name__ == "__main__":
    import uvicorn
    print("🚀 Starting Multiple Image to PDF Converter")
    print("=" * 60)
    print("🎯 FEATURES:")
    print("   📚 Process multiple images in memory")
    print("   📄 Generate PDF from images")
    print("   💾 Save only PDF files (images not stored)")
    print("   📥 Download generated PDFs")
    print("=" * 60)
    print("🌐 Open: http://localhost:8001")
    print("💡 TIP: Hold Ctrl/Cmd to select multiple images!")

    uvicorn.run(app, host="0.0.0.0", port=8001)
