"""
Multiple Image Upload & PDF Generation Demo
Complete implementation of multiple image upload with automatic PDF generation
"""

from fastapi import FastAPI, UploadFile, File, Form, HTTPException, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import FileResponse, HTMLResponse
from fastapi.staticfiles import StaticFiles
from typing import List, Optional
import os
import uuid
from PIL import Image
from reportlab.pdfgen import canvas
from reportlab.lib.pagesizes import letter, landscape
from sqlalchemy import create_engine, Column, Integer, String, DateTime, Text
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, Session
from datetime import datetime
import tempfile

# Database setup (using SQLite for simplicity)
SQLALCHEMY_DATABASE_URL = "sqlite:///./documents.db"
engine = create_engine(SQLALCHEMY_DATABASE_URL, connect_args={"check_same_thread": False})
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
Base = declarative_base()

# Document model
class Document(Base):
    __tablename__ = "documents"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, index=True)
    file_path = Column(String)
    mime_type = Column(String)
    size = Column(Integer)
    status = Column(String, default="completed")
    created_at = Column(DateTime, default=datetime.utcnow)

# Create tables
Base.metadata.create_all(bind=engine)

# FastAPI app
app = FastAPI(
    title="Multiple Image Upload & PDF Generation Demo",
    description="Upload multiple images and generate PDF documents",
    version="1.0.0"
)

# Add CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Create uploads directory
os.makedirs("uploads", exist_ok=True)

# Dependency to get database session
def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

# Helper functions
def save_uploaded_file(file: UploadFile) -> str:
    """Save uploaded file and return file path."""
    filename = file.filename or f"file_{uuid.uuid4()}"
    file_extension = os.path.splitext(filename)[1]
    unique_filename = f"{uuid.uuid4()}{file_extension}"
    file_path = os.path.join("uploads", unique_filename)
    
    with open(file_path, "wb") as buffer:
        content = file.file.read()
        buffer.write(content)
    
    return file_path

def create_pdf_from_images(image_paths: List[str], pdf_name: str, orientation: str = "portrait") -> str:
    """Create PDF from multiple images."""
    pagesize = landscape(letter) if orientation == "landscape" else letter
    
    pdf_filename = f"{pdf_name}_{uuid.uuid4().hex[:8]}.pdf"
    pdf_path = os.path.join("uploads", pdf_filename)
    
    c = canvas.Canvas(pdf_path, pagesize=pagesize)
    page_width, page_height = pagesize
    
    for img_path in image_paths:
        try:
            # Get image dimensions
            with Image.open(img_path) as img:
                img_width, img_height = img.size
            
            # Calculate scaling to fit page
            margin = 50
            available_width = page_width - 2 * margin
            available_height = page_height - 2 * margin
            
            scale_x = available_width / img_width
            scale_y = available_height / img_height
            scale = min(scale_x, scale_y)
            
            new_width = img_width * scale
            new_height = img_height * scale
            
            # Center the image
            x = margin + (available_width - new_width) / 2
            y = margin + (available_height - new_height) / 2
            
            # Add image to PDF
            c.drawImage(img_path, x, y, width=new_width, height=new_height)
            c.showPage()
            
        except Exception as e:
            print(f"Error processing image {img_path}: {e}")
            continue
    
    c.save()
    return pdf_path

# API Endpoints
@app.get("/")
def root():
    return {
        "message": "Document Processing Platform - Main Project",
        "status": "running",
        "features": [
            "Multiple image upload in one go",
            "Automatic PDF generation",
            "Database storage",
            "Professional PDF layout"
        ],
        "endpoints": {
            "docs": "/docs",
            "test": "/test",
            "upload_multiple": "/api/v1/documents/upload-multiple",
            "images_to_pdf": "/api/v1/documents/images-to-pdf",
            "documents": "/api/v1/documents",
            "download": "/download/{filename}"
        }
    }

@app.get("/test", response_class=HTMLResponse)
def test_interface():
    """Serve the HTML test interface for manual testing."""
    try:
        with open("backend/test_interface.html", "r", encoding="utf-8") as f:
            return HTMLResponse(content=f.read())
    except FileNotFoundError:
        return HTMLResponse(content="<h1>Test interface not found</h1>", status_code=404)

@app.post("/api/v1/documents/upload-multiple",
          summary="Upload Multiple Images & Generate PDF",
          description="Upload multiple images at once and optionally generate a combined PDF document")
async def upload_multiple_documents(
    files: List[UploadFile] = File(..., description="Multiple image files (PNG, JPG, JPEG)"),
    generate_pdf: bool = Form(False, description="Generate PDF from uploaded images"),
    pdf_name: Optional[str] = Form("Combined_Images", description="Name for the generated PDF"),
    db: Session = Depends(get_db)
):
    """Upload multiple files and optionally generate PDF - Main Project Implementation."""
    
    if not files:
        raise HTTPException(status_code=400, detail="No files provided")
    
    uploaded_files = []
    uploaded_paths = []
    
    # Upload all files
    for file in files:
        try:
            # Validate file type
            if not file.content_type or not file.content_type.startswith('image/'):
                uploaded_files.append({
                    "name": file.filename,
                    "status": "failed",
                    "message": "Invalid file type - only images allowed"
                })
                continue
            
            # Save file
            file_path = save_uploaded_file(file)
            file_size = os.path.getsize(file_path)
            
            # Save to database
            document = Document(
                name=file.filename,
                file_path=file_path,
                mime_type=file.content_type,
                size=file_size,
                status="completed"
            )
            db.add(document)
            db.commit()
            db.refresh(document)
            
            uploaded_files.append({
                "id": document.id,
                "name": file.filename,
                "status": "uploaded",
                "size": file_size,
                "message": "Image uploaded successfully"
            })
            
            uploaded_paths.append(file_path)
            
        except Exception as e:
            uploaded_files.append({
                "name": file.filename or "unknown",
                "status": "failed",
                "message": f"Upload failed: {str(e)}"
            })
    
    pdf_result = None
    
    # Generate PDF if requested
    if generate_pdf and uploaded_paths and pdf_name:
        try:
            pdf_path = create_pdf_from_images(uploaded_paths, pdf_name)
            pdf_size = os.path.getsize(pdf_path)
            pdf_filename = os.path.basename(pdf_path)
            
            # Save PDF to database
            pdf_document = Document(
                name=f"{pdf_name}.pdf",
                file_path=pdf_path,
                mime_type="application/pdf",
                size=pdf_size,
                status="completed"
            )
            db.add(pdf_document)
            db.commit()
            db.refresh(pdf_document)
            
            pdf_result = {
                "pdf_id": pdf_document.id,
                "pdf_name": pdf_filename,
                "pdf_size": pdf_size,
                "pages": len(uploaded_paths),
                "status": "generated",
                "download_url": f"/download/{pdf_filename}"
            }
            
        except Exception as e:
            pdf_result = {
                "status": "failed",
                "error": str(e)
            }
    
    return {
        "uploaded_files": uploaded_files,
        "uploaded_count": len([f for f in uploaded_files if f["status"] == "uploaded"]),
        "pdf_result": pdf_result,
        "message": f"✅ Processed {len(uploaded_files)} files" + (
            f", PDF generated!" if pdf_result and pdf_result.get("status") == "generated" else ""
        )
    }

@app.post("/api/v1/documents/images-to-pdf",
          summary="🖼️➡️📄 Convert Existing Images to PDF - Main Project",
          description="Convert previously uploaded images into a single PDF with custom configuration.")
def convert_images_to_pdf(
    request: dict,
    db: Session = Depends(get_db)
):
    """Convert existing images to PDF - Main Project Implementation."""
    
    document_ids = request.get("document_ids", [])
    pdf_name = request.get("pdf_name", "converted_images")
    page_orientation = request.get("page_orientation", "portrait")
    
    if not document_ids:
        raise HTTPException(status_code=400, detail="No document IDs provided")
    
    # Get documents from database
    documents = db.query(Document).filter(
        Document.id.in_(document_ids),
        Document.mime_type.like("image/%")
    ).all()
    
    if not documents:
        raise HTTPException(status_code=404, detail="No image documents found")
    
    try:
        # Get image paths
        image_paths = [doc.file_path for doc in documents]
        
        # Create PDF
        pdf_path = create_pdf_from_images(image_paths, pdf_name, page_orientation)
        pdf_size = os.path.getsize(pdf_path)
        pdf_filename = os.path.basename(pdf_path)
        
        # Save PDF to database
        pdf_document = Document(
            name=f"{pdf_name}.pdf",
            file_path=pdf_path,
            mime_type="application/pdf",
            size=pdf_size,
            status="completed"
        )
        db.add(pdf_document)
        db.commit()
        db.refresh(pdf_document)
        
        return {
            "pdf_id": pdf_document.id,
            "pdf_name": pdf_filename,
            "pdf_size": pdf_size,
            "pages": len(documents),
            "orientation": page_orientation,
            "status": "generated",
            "message": f"PDF created from {len(documents)} images",
            "download_url": f"/download/{pdf_filename}"
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"PDF generation failed: {str(e)}")

@app.get("/api/v1/documents",
         summary="📋 List All Documents - Main Project",
         description="Get a list of all uploaded images and generated PDFs from the database.")
def get_documents(db: Session = Depends(get_db)):
    """Get all documents from database."""
    
    documents = db.query(Document).order_by(Document.created_at.desc()).all()
    
    document_list = []
    for doc in documents:
        document_list.append({
            "id": doc.id,
            "name": doc.name,
            "mime_type": doc.mime_type,
            "size": doc.size,
            "status": doc.status,
            "created_at": doc.created_at.isoformat(),
            "type": "pdf" if doc.mime_type == "application/pdf" else "image"
        })
    
    images = [d for d in document_list if d["type"] == "image"]
    pdfs = [d for d in document_list if d["type"] == "pdf"]
    
    return {
        "documents": document_list,
        "summary": {
            "total": len(document_list),
            "images": len(images),
            "pdfs": len(pdfs)
        },
        "message": f"Found {len(images)} images and {len(pdfs)} PDFs in database"
    }

@app.get("/download/{filename}",
         summary="📥 Download File - Main Project",
         description="Download a generated PDF or uploaded image file.")
def download_file(filename: str):
    """Download a file."""
    file_path = os.path.join("uploads", filename)
    if os.path.exists(file_path):
        return FileResponse(file_path, filename=filename)
    else:
        raise HTTPException(status_code=404, detail="File not found")

if __name__ == "__main__":
    import uvicorn
    print("🚀 Starting Multiple Image Upload & PDF Generation Demo")
    print("=" * 60)
    print("🎯 DEMO FEATURES:")
    print("   📚 Upload multiple images at once (hold Ctrl/Cmd)")
    print("   📄 Automatic PDF generation from images")
    print("   💾 Database storage with SQLite")
    print("   🎨 Professional PDF layout with ReportLab")
    print("   📥 Download generated PDFs")
    print("=" * 60)
    print("📖 Demo URL: http://localhost:8001/docs")
    print("🎯 Key endpoint: POST /api/v1/documents/upload-multiple")
    print("💡 TIP: Hold Ctrl (Windows) or Cmd (Mac) to select multiple images!")

    uvicorn.run(app, host="0.0.0.0", port=8001)
