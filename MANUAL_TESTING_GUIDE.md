# 🧪 Manual Testing Guide - Multiple Image Upload & PDF Generation

## 🌐 **Testing Server Access**
- **URL**: http://localhost:8002/docs
- **Status**: ✅ Running and ready for testing
- **Authentication**: ❌ Not required

---

## 🎯 **What You're Testing**

### **Main Feature**: Multiple Image Upload in One Go
- Select multiple images at once (not one by one)
- Automatically generate PDF from uploaded images
- Professional PDF layout and scaling

---

## 📋 **Step-by-Step Manual Testing**

### **Test 1: Single Image Upload** 📁
1. **Go to**: http://localhost:8002/docs
2. **Find**: `POST /upload` endpoint
3. **Click**: "Try it out"
4. **Upload**: Select ONE image file (PNG/JPG)
5. **Click**: "Execute"
6. **Expected**: ✅ Image uploaded with ID assigned

---

### **Test 2: Multiple Image Upload (KEY FEATURE)** 📚
1. **Find**: `POST /upload-multiple` endpoint
2. **Click**: "Try it out"
3. **IMPORTANT**: In the file picker:
   - **Hold Ctrl (Windows) or Cmd (Mac)**
   - **Click multiple image files** (3-5 images)
   - **All files should be selected at once**
4. **Set Parameters**:
   - `generate_pdf`: `true`
   - `pdf_name`: `"My_Test_Combined_PDF"`
5. **Click**: "Execute"
6. **Expected**: ✅ All images uploaded + PDF automatically generated

---

### **Test 3: Check Results** 📋
1. **Find**: `GET /documents` endpoint
2. **Click**: "Try it out"
3. **Click**: "Execute"
4. **Expected**: List showing:
   - Individual uploaded images
   - Generated PDF files
   - File sizes and types

---

### **Test 4: Convert Existing Images to PDF** 🖼️➡️📄
1. **First**: Note down image IDs from Test 3
2. **Find**: `POST /images-to-pdf` endpoint
3. **Click**: "Try it out"
4. **Enter Request Body**:
```json
{
  "document_ids": [1, 2, 3],
  "pdf_name": "Manual_Conversion_Test",
  "page_orientation": "portrait",
  "image_fit": "contain"
}
```
5. **Click**: "Execute"
6. **Expected**: ✅ New PDF created from selected images

---

### **Test 5: Download Generated PDFs** 📥
1. **From Test 3 results**, note PDF filenames
2. **Find**: `GET /download/{filename}` endpoint
3. **Click**: "Try it out"
4. **Enter**: PDF filename (e.g., "My_Test_Combined_PDF_a1b2c3d4.pdf")
5. **Click**: "Execute"
6. **Expected**: ✅ PDF file downloads to your computer

---

## 🎯 **Key Things to Test**

### **Multiple File Selection Methods**:

#### **Method 1: Ctrl/Cmd Multi-Select**
- Click file picker
- **Hold Ctrl (Windows) or Cmd (Mac)**
- **Click multiple images** one by one
- All should be selected together
- Click "Open"

#### **Method 2: Shift Range Select**
- Click file picker
- Click first image
- **Hold Shift**
- Click last image
- All images in between should be selected

#### **Method 3: Select All in Folder**
- Navigate to folder with images
- **Ctrl+A (Windows) or Cmd+A (Mac)** to select all
- Click "Open"

---

## ✅ **Expected Success Results**

### **Single Upload Response**:
```json
{
  "id": 1,
  "name": "your_image.png",
  "status": "uploaded",
  "size": 12345,
  "message": "✅ Image uploaded successfully! ID: 1"
}
```

### **Multiple Upload Response**:
```json
{
  "uploaded_files": [
    {"id": 1, "name": "image1.png", "status": "uploaded"},
    {"id": 2, "name": "image2.png", "status": "uploaded"},
    {"id": 3, "name": "image3.png", "status": "uploaded"}
  ],
  "uploaded_count": 3,
  "pdf_result": {
    "pdf_id": 4,
    "pdf_name": "My_Test_Combined_PDF_a1b2c3d4.pdf",
    "pdf_size": 45678,
    "pages": 3,
    "status": "generated",
    "download_url": "/download/My_Test_Combined_PDF_a1b2c3d4.pdf"
  },
  "message": "✅ Processed 3 files, PDF generated!"
}
```

---

## 🔧 **Troubleshooting**

### **If Multiple Selection Doesn't Work**:
1. **Make sure** you're holding Ctrl/Cmd while clicking
2. **Try different browser** (Chrome, Firefox, Edge)
3. **Check file types** - only select image files (PNG, JPG, JPEG)
4. **Try smaller number** of files first (2-3 images)

### **If PDF Generation Fails**:
1. **Check file sizes** - keep under 10MB each
2. **Use valid image formats** - PNG, JPG, JPEG only
3. **Provide PDF name** - don't leave empty
4. **Check server logs** for error details

### **If Download Fails**:
1. **Copy exact filename** from the response
2. **Include file extension** (.pdf)
3. **Check if PDF was actually generated** in documents list

---

## 🎉 **Success Criteria**

You've successfully tested the feature if:

✅ **Multiple images selected** in one file picker dialog  
✅ **All images uploaded** simultaneously  
✅ **PDF automatically generated** from uploaded images  
✅ **PDF downloads** successfully  
✅ **Professional PDF layout** with proper image scaling  

---

## 📞 **Need Help?**

If you encounter any issues:
1. **Check browser console** for JavaScript errors
2. **Look at server logs** in the terminal
3. **Try with different image files**
4. **Test with smaller file sizes**
5. **Use different browsers**

**The server is running at http://localhost:8002/docs - Start testing now!** 🚀
