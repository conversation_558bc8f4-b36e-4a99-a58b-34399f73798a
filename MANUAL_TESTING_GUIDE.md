# 🧪 Manual Testing Guide - Multiple Image Upload & PDF Generation

## 🔐 Test Credentials

### Primary Test Account
- **📧 Email**: `<EMAIL>`
- **🔑 Password**: `testpassword123`
- **👤 Username**: `testuser`

### Additional Test Accounts
- **📧 Email**: `<EMAIL>` | **🔑 Password**: `admin123`
- **📧 Email**: `<EMAIL>` | **🔑 Password**: `demo123`

## 🚀 Setup Instructions

### 1. Start the Application
```bash
# Terminal 1: Start Backend
cd backend
python -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8000

# Terminal 2: Start Frontend (if Node.js available)
cd frontend
npm start

# Terminal 3: Start Redis (if needed)
redis-server

# Terminal 4: Start Celery Worker (if needed)
cd backend
celery -A app.celery_app worker --loglevel=info
```

### 2. Create Test User
```bash
cd backend
python create_test_user.py
```

### 3. Access the Application
- **Frontend**: http://localhost:3000
- **Backend API**: http://localhost:8000
- **API Docs**: http://localhost:8000/docs

## 🧪 Manual Test Cases

### Test Case 1: User Authentication
**Objective**: Verify login functionality

**Steps**:
1. Navigate to http://localhost:3000
2. Click "Login" or navigate to login page
3. Enter credentials:
   - Email: `<EMAIL>`
   - Password: `testpassword123`
4. Click "Login"

**Expected Result**: ✅ Successfully logged in and redirected to dashboard

---

### Test Case 2: Single Image Upload
**Objective**: Test basic single image upload

**Steps**:
1. Navigate to Documents page
2. Go to "Upload Documents" tab
3. Drag and drop a single image file (PNG/JPG)
4. Verify upload progress and completion

**Expected Result**: ✅ Image uploaded successfully, appears in document list

---

### Test Case 3: Multiple Image Upload (No PDF)
**Objective**: Test multiple image upload without PDF generation

**Steps**:
1. Navigate to Documents page → "Upload Documents" tab
2. Select multiple image files (3-5 images)
3. When prompted, choose "Upload Files" (without PDF generation)
4. Monitor upload progress

**Expected Result**: ✅ All images uploaded individually, appear in document list

---

### Test Case 4: Multiple Image Upload with PDF Generation
**Objective**: Test automatic PDF generation during upload

**Steps**:
1. Navigate to Documents page → "Upload Documents" tab
2. Select multiple image files (3-5 images)
3. When prompted:
   - ✅ Check "Generate combined PDF"
   - Enter PDF name: "Test_Combined_Images"
4. Click "Upload & Generate PDF"
5. Monitor upload and PDF generation status

**Expected Result**: 
- ✅ All images uploaded
- ✅ PDF generation started (status: "processing")
- ✅ Combined PDF appears in document list when complete

---

### Test Case 5: Convert Existing Images to PDF
**Objective**: Test PDF conversion from existing documents

**Steps**:
1. Navigate to Documents page → "Convert Images to PDF" tab
2. Select multiple uploaded images from the grid
3. Configure PDF settings:
   - PDF Name: "Manual_Conversion_Test"
   - Page Orientation: "Portrait"
   - Image Fit: "Contain"
4. Click "Convert X Images to PDF"
5. Monitor conversion status

**Expected Result**: 
- ✅ PDF conversion started
- ✅ Status shows "processing"
- ✅ New PDF document appears in list

---

### Test Case 6: Different PDF Configurations
**Objective**: Test various PDF generation options

**Steps**:
1. Repeat Test Case 5 with different configurations:
   - **Test 6a**: Landscape orientation
   - **Test 6b**: "Cover" image fit mode
   - **Test 6c**: "Fill" image fit mode
2. Compare resulting PDFs

**Expected Result**: ✅ PDFs generated with different layouts and orientations

---

### Test Case 7: File Type Validation
**Objective**: Test file upload validation

**Steps**:
1. Try uploading invalid file types:
   - .txt file
   - .exe file
   - .docx file
2. Try uploading oversized files (>10MB)

**Expected Result**: 
- ✅ Invalid files rejected with clear error messages
- ✅ Oversized files rejected
- ✅ User informed of allowed file types

---

### Test Case 8: Error Handling
**Objective**: Test error scenarios

**Steps**:
1. **Test 8a**: Try PDF conversion with no images selected
2. **Test 8b**: Try PDF conversion with empty PDF name
3. **Test 8c**: Upload corrupted image file
4. **Test 8d**: Test with network interruption (if possible)

**Expected Result**: 
- ✅ Clear error messages displayed
- ✅ User can recover from errors
- ✅ Application remains stable

---

### Test Case 9: User Interface Testing
**Objective**: Test UI/UX functionality

**Steps**:
1. **Test 9a**: Test drag-and-drop functionality
2. **Test 9b**: Test file selection dialog
3. **Test 9c**: Test progress indicators
4. **Test 9d**: Test status notifications
5. **Test 9e**: Test responsive design (resize browser)

**Expected Result**: 
- ✅ Intuitive drag-and-drop works
- ✅ Progress bars show correctly
- ✅ Status messages are clear
- ✅ Interface adapts to screen size

---

### Test Case 10: Document Management
**Objective**: Test document viewing and management

**Steps**:
1. View document list
2. Click on individual documents
3. Test document deletion
4. Test document search/filtering (if available)
5. Download generated PDFs

**Expected Result**: 
- ✅ Documents display correctly
- ✅ Document details accessible
- ✅ PDFs can be downloaded and viewed
- ✅ Management functions work

## 📊 Test Results Template

### Test Execution Log
```
Date: ___________
Tester: ___________
Environment: Development/Staging/Production

Test Case 1 - User Authentication: ✅ PASS / ❌ FAIL
Notes: ________________________________

Test Case 2 - Single Image Upload: ✅ PASS / ❌ FAIL  
Notes: ________________________________

Test Case 3 - Multiple Upload (No PDF): ✅ PASS / ❌ FAIL
Notes: ________________________________

Test Case 4 - Multiple Upload (With PDF): ✅ PASS / ❌ FAIL
Notes: ________________________________

Test Case 5 - Convert Existing Images: ✅ PASS / ❌ FAIL
Notes: ________________________________

Test Case 6 - PDF Configurations: ✅ PASS / ❌ FAIL
Notes: ________________________________

Test Case 7 - File Validation: ✅ PASS / ❌ FAIL
Notes: ________________________________

Test Case 8 - Error Handling: ✅ PASS / ❌ FAIL
Notes: ________________________________

Test Case 9 - UI/UX Testing: ✅ PASS / ❌ FAIL
Notes: ________________________________

Test Case 10 - Document Management: ✅ PASS / ❌ FAIL
Notes: ________________________________
```

## 🔧 Troubleshooting

### Common Issues & Solutions

**Issue**: Login fails
- **Solution**: Ensure test user was created, check credentials

**Issue**: File upload fails
- **Solution**: Check file size (<10MB), verify file type (PNG/JPG/PDF)

**Issue**: PDF generation doesn't start
- **Solution**: Ensure Celery worker is running, check Redis connection

**Issue**: Frontend not loading
- **Solution**: Ensure backend is running on port 8000, check CORS settings

**Issue**: Images not displaying
- **Solution**: Check file paths, ensure uploads directory exists

## 📞 Support

If you encounter any issues during testing:
1. Check browser console for errors
2. Check backend logs for error messages
3. Verify all services are running (backend, frontend, Redis, Celery)
4. Ensure test data is properly created

## 🎯 Success Criteria

The manual testing is successful if:
- ✅ All 10 test cases pass
- ✅ No critical errors encountered
- ✅ User experience is intuitive
- ✅ PDF generation works correctly
- ✅ File validation works properly
- ✅ Error handling is graceful
