# Multiple Image to PDF Converter - Frontend

A modern React TypeScript frontend for the Multiple Image to PDF Converter application.

## 🎯 Features

- **Modern React with TypeScript** - Type-safe development
- **Tailwind CSS** - Beautiful, responsive design
- **React Dropzone** - Drag & drop file upload
- **Axios** - HTTP client for API communication
- **React Router** - Client-side routing

## 🚀 Quick Start

### Prerequisites
- Node.js 16+ and npm
- Backend server running on http://localhost:8001

### Installation
```bash
npm install
```

### Development
```bash
npm start
```
Opens http://localhost:3000

### Production Build
```bash
npm run build
```

## 📁 Project Structure

```
frontend/
├── src/
│   ├── components/
│   │   ├── common/
│   │   │   └── Header.tsx          # Navigation header
│   │   └── documents/
│   │       └── MultipleImageUpload.tsx  # Main upload component
│   ├── pages/
│   │   ├── HomePage.tsx            # Landing page with converter
│   │   └── DocumentsPage.tsx       # Document library
│   ├── services/
│   │   └── api.ts                  # API client configuration
│   ├── App.tsx                     # Main app component
│   ├── index.tsx                   # App entry point
│   └── index.css                   # Global styles
├── package.json
└── README.md
```

## 🎨 Components

### MultipleImageUpload
- Drag & drop interface
- Multiple file selection
- Image preview with thumbnails
- Progress tracking
- Success/error handling
- PDF download functionality

### HomePage
- Hero section with call-to-action
- Feature highlights
- Integrated upload component
- Responsive design

### DocumentsPage
- Document library with table view
- Summary statistics
- Download functionality
- Responsive design

### Header
- Navigation between pages
- Modern gradient design
- Active page highlighting

## 🔧 Configuration

### API Endpoint
Update `src/services/api.ts` to change the backend URL:
```typescript
const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8001';
```

### Environment Variables
Create `.env` file:
```
REACT_APP_API_URL=http://localhost:8001
```

## 🎯 Key Features Implemented

### 1. Multiple Image Upload
- **Drag & Drop**: Intuitive file selection
- **Multiple Selection**: Ctrl/Cmd + click support
- **File Validation**: Image format checking
- **Preview**: Thumbnail grid with remove options
- **Progress**: Real-time upload progress

### 2. PDF Generation
- **Automatic Processing**: Images converted to PDF
- **Custom Naming**: User-defined PDF names
- **Download**: Direct PDF download links
- **Status Tracking**: Success/error feedback

### 3. Document Management
- **Library View**: Table of all documents
- **Statistics**: Summary cards with counts
- **Download**: One-click PDF downloads
- **Responsive**: Mobile-friendly design

### 4. User Experience
- **Modern Design**: Clean, professional interface
- **Animations**: Smooth transitions and feedback
- **Responsive**: Works on all screen sizes
- **Accessibility**: Keyboard navigation support

## 🎨 Design System

### Colors
- **Primary**: Blue gradient (blue-600 to purple-600)
- **Success**: Green (green-600)
- **Error**: Red (red-600)
- **Background**: Gray-50

### Typography
- **Font**: Inter (system fallback)
- **Headings**: Bold, large sizes
- **Body**: Regular weight, readable sizes

### Components
- **Cards**: White background, subtle shadows
- **Buttons**: Rounded, hover effects
- **Inputs**: Clean borders, focus states

## 📱 Responsive Design

- **Mobile**: Single column layout
- **Tablet**: Adapted grid layouts
- **Desktop**: Full multi-column experience

## 🔄 State Management

- **Local State**: React useState for component state
- **API Calls**: Axios with error handling
- **File Management**: Object URLs for previews

## 🚀 Performance

- **Code Splitting**: React.lazy for route-based splitting
- **Image Optimization**: Object URL cleanup
- **API Optimization**: Efficient data fetching

## 🧪 Testing

The frontend is designed to work seamlessly with the backend API:
- Upload endpoint: `POST /api/v1/documents/upload-multiple`
- Documents endpoint: `GET /api/v1/documents`
- Download endpoint: `GET /download/{filename}`

## 🎯 Browser Support

- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

## 📞 Support

For issues or questions:
1. Check backend server is running on port 8001
2. Verify API endpoints are accessible
3. Check browser console for errors
4. Ensure file formats are supported (PNG, JPG, JPEG)

---

**🎉 The frontend is ready to provide a modern, user-friendly interface for the Multiple Image to PDF Converter!**
