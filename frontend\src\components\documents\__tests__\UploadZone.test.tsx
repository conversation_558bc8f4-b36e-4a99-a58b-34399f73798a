import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { UploadZone } from '../UploadZone';
import { useDocuments } from '../../../hooks/useDocuments';

// Mock the useDocuments hook
jest.mock('../../../hooks/useDocuments');
const mockUseDocuments = useDocuments as jest.MockedFunction<typeof useDocuments>;

// Mock react-dropzone
jest.mock('react-dropzone', () => ({
  useDropzone: jest.fn(({ onDrop }) => ({
    getRootProps: () => ({
      'data-testid': 'dropzone'
    }),
    getInputProps: () => ({
      'data-testid': 'file-input'
    }),
    isDragActive: false
  }))
}));

describe('UploadZone', () => {
  const mockUploadDocument = jest.fn();
  const mockOnMultipleUpload = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
    mockUseDocuments.mockReturnValue({
      documents: [],
      loading: false,
      error: null,
      fetchDocuments: jest.fn(),
      uploadDocument: mockUploadDocument,
      uploadMultipleDocuments: jest.fn(),
      convertImagesToPdf: jest.fn(),
      deleteDocument: jest.fn()
    });
  });

  it('renders upload zone with correct text', () => {
    render(<UploadZone />);
    
    expect(screen.getByText('Drag and drop files here, or click to select files')).toBeInTheDocument();
    expect(screen.getByText('Supported formats: PDF, PNG, JPG, TIFF (max 10MB each)')).toBeInTheDocument();
    expect(screen.getByText('Select multiple images to combine into a PDF')).toBeInTheDocument();
  });

  it('calls uploadDocument for single file', async () => {
    const { useDropzone } = require('react-dropzone');
    let onDropCallback: any;
    
    useDropzone.mockImplementation(({ onDrop }: any) => {
      onDropCallback = onDrop;
      return {
        getRootProps: () => ({ 'data-testid': 'dropzone' }),
        getInputProps: () => ({ 'data-testid': 'file-input' }),
        isDragActive: false
      };
    });

    render(<UploadZone />);
    
    const file = new File(['test'], 'test.pdf', { type: 'application/pdf' });
    await onDropCallback([file]);
    
    expect(mockUploadDocument).toHaveBeenCalledWith(file);
  });

  it('shows PDF options modal for multiple images', async () => {
    const { useDropzone } = require('react-dropzone');
    let onDropCallback: any;
    
    useDropzone.mockImplementation(({ onDrop }: any) => {
      onDropCallback = onDrop;
      return {
        getRootProps: () => ({ 'data-testid': 'dropzone' }),
        getInputProps: () => ({ 'data-testid': 'file-input' }),
        isDragActive: false
      };
    });

    render(<UploadZone onMultipleUpload={mockOnMultipleUpload} />);
    
    const files = [
      new File(['test1'], 'test1.png', { type: 'image/png' }),
      new File(['test2'], 'test2.jpg', { type: 'image/jpeg' })
    ];
    
    await onDropCallback(files);
    
    await waitFor(() => {
      expect(screen.getByText('Combine 2 images into PDF?')).toBeInTheDocument();
    });
  });

  it('handles PDF generation option correctly', async () => {
    const { useDropzone } = require('react-dropzone');
    let onDropCallback: any;
    
    useDropzone.mockImplementation(({ onDrop }: any) => {
      onDropCallback = onDrop;
      return {
        getRootProps: () => ({ 'data-testid': 'dropzone' }),
        getInputProps: () => ({ 'data-testid': 'file-input' }),
        isDragActive: false
      };
    });

    render(<UploadZone onMultipleUpload={mockOnMultipleUpload} />);
    
    const files = [
      new File(['test1'], 'test1.png', { type: 'image/png' }),
      new File(['test2'], 'test2.jpg', { type: 'image/jpeg' })
    ];
    
    await onDropCallback(files);
    
    await waitFor(() => {
      expect(screen.getByText('Combine 2 images into PDF?')).toBeInTheDocument();
    });

    // Check the PDF generation checkbox
    const checkbox = screen.getByRole('checkbox');
    fireEvent.click(checkbox);

    // Enter PDF name
    const pdfNameInput = screen.getByPlaceholderText('Enter PDF name');
    fireEvent.change(pdfNameInput, { target: { value: 'my_combined_pdf' } });

    // Click upload button
    const uploadButton = screen.getByText('Upload & Generate PDF');
    fireEvent.click(uploadButton);

    expect(mockOnMultipleUpload).toHaveBeenCalledWith(files, true, 'my_combined_pdf');
  });

  it('handles cancel action in PDF modal', async () => {
    const { useDropzone } = require('react-dropzone');
    let onDropCallback: any;
    
    useDropzone.mockImplementation(({ onDrop }: any) => {
      onDropCallback = onDrop;
      return {
        getRootProps: () => ({ 'data-testid': 'dropzone' }),
        getInputProps: () => ({ 'data-testid': 'file-input' }),
        isDragActive: false
      };
    });

    render(<UploadZone />);
    
    const files = [
      new File(['test1'], 'test1.png', { type: 'image/png' }),
      new File(['test2'], 'test2.jpg', { type: 'image/jpeg' })
    ];
    
    await onDropCallback(files);
    
    await waitFor(() => {
      expect(screen.getByText('Combine 2 images into PDF?')).toBeInTheDocument();
    });

    // Click cancel button
    const cancelButton = screen.getByText('Cancel');
    fireEvent.click(cancelButton);

    // Modal should be closed
    await waitFor(() => {
      expect(screen.queryByText('Combine 2 images into PDF?')).not.toBeInTheDocument();
    });
  });

  it('calls onMultipleUpload for multiple non-image files', async () => {
    const { useDropzone } = require('react-dropzone');
    let onDropCallback: any;
    
    useDropzone.mockImplementation(({ onDrop }: any) => {
      onDropCallback = onDrop;
      return {
        getRootProps: () => ({ 'data-testid': 'dropzone' }),
        getInputProps: () => ({ 'data-testid': 'file-input' }),
        isDragActive: false
      };
    });

    render(<UploadZone onMultipleUpload={mockOnMultipleUpload} />);
    
    const files = [
      new File(['test1'], 'test1.pdf', { type: 'application/pdf' }),
      new File(['test2'], 'test2.txt', { type: 'text/plain' })
    ];
    
    await onDropCallback(files);
    
    expect(mockOnMultipleUpload).toHaveBeenCalledWith(files);
  });
});
