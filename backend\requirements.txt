# ============================================================================
# MULTIPLE IMAGE TO PDF CONVERTER - PRODUCTION DEPENDENCIES
# ============================================================================

# Core Web Framework
fastapi==0.104.1
uvicorn[standard]==0.24.0
python-multipart==0.0.6

# Database and ORM
sqlalchemy==2.0.23

# Configuration and Settings
pydantic==2.5.0
pydantic-settings==2.1.0

# Image Processing and PDF Generation
pillow==10.1.0
reportlab==4.0.7

# Development and Testing (Optional)
pytest==7.4.3
pytest-asyncio==0.21.1

# ============================================================================
# INSTALLATION INSTRUCTIONS
# ============================================================================
#
# Install core dependencies:
# pip install fastapi uvicorn python-multipart sqlalchemy pydantic pydantic-settings pillow reportlab
#
# For development:
# pip install -r requirements.txt
#
# ============================================================================
