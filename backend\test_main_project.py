"""
Comprehensive test of the main project multiple image upload functionality
"""

import requests
from PIL import Image
import tempfile
import os
import json

def create_test_images(count=4):
    """Create test images for testing."""
    images = []
    colors = ['red', 'green', 'blue', 'yellow', 'purple']
    sizes = [(400, 300), (500, 400), (350, 450), (600, 400), (450, 350)]
    
    for i in range(count):
        # Create temporary file
        tmp_file = tempfile.NamedTemporaryFile(suffix=f'_test_{i}.png', delete=False)
        tmp_file.close()
        
        # Create test image
        color = colors[i % len(colors)]
        size = sizes[i % len(sizes)]
        
        img = Image.new('RGB', size, color=color)
        
        # Add some text/pattern to make images distinct
        from PIL import ImageDraw
        draw = ImageDraw.Draw(img)
        
        # Draw some shapes to make it look like a document
        draw.rectangle([20, 20, size[0]-20, 60], fill='white')
        draw.text((30, 30), f"Test Document {i+1} - {color.upper()}", fill='black')
        
        # Add some content rectangles
        for j in range(3):
            y_pos = 100 + j * 80
            draw.rectangle([30, y_pos, size[0]-30, y_pos+50], outline='black', width=2)
            draw.text((40, y_pos+15), f"Content section {j+1}", fill='black')
        
        img.save(tmp_file.name)
        
        images.append({
            'path': tmp_file.name,
            'name': f'test_document_{i+1}_{color}.png',
            'color': color,
            'size': size
        })
        
        print(f"✅ Created test image {i+1}: {color} ({size[0]}x{size[1]})")
    
    return images

def test_main_project_functionality():
    """Test the main project multiple image upload functionality."""
    base_url = "http://localhost:8000"
    
    print("🧪 TESTING MAIN PROJECT MULTIPLE IMAGE UPLOAD")
    print("=" * 60)
    
    # Test 1: Check server status
    print("\n📡 Test 1: Server Status Check")
    try:
        response = requests.get(f"{base_url}/")
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Server is running")
            print(f"   Message: {result['message']}")
            print(f"   Status: {result['status']}")
            print(f"   Features: {', '.join(result['features'])}")
        else:
            print(f"❌ Server returned status code: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Cannot connect to server: {e}")
        return False
    
    # Test 2: Create test images
    print("\n🖼️ Test 2: Creating Test Images")
    test_images = create_test_images(4)
    
    # Test 3: Test multiple image upload with PDF generation
    print("\n📚 Test 3: Multiple Image Upload with PDF Generation")
    try:
        # Prepare files for upload
        files = []
        for img in test_images:
            with open(img['path'], 'rb') as f:
                files.append(('files', (img['name'], f.read(), 'image/png')))
        
        # Prepare form data
        data = {
            'generate_pdf': 'true',
            'pdf_name': 'Main_Project_Test_Combined_PDF'
        }
        
        print(f"   📤 Uploading {len(test_images)} images...")
        print(f"   📄 Requesting PDF generation: {data['pdf_name']}")
        
        response = requests.post(f"{base_url}/api/v1/documents/upload-multiple", 
                               files=files, data=data)
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Multiple upload successful!")
            print(f"   📁 Uploaded files: {result['uploaded_count']}")
            print(f"   💬 Message: {result['message']}")
            
            # Check PDF generation
            if result.get('pdf_result'):
                pdf_info = result['pdf_result']
                if pdf_info.get('status') == 'generated':
                    print(f"   📄 PDF generated successfully!")
                    print(f"      PDF ID: {pdf_info['pdf_id']}")
                    print(f"      PDF Name: {pdf_info['pdf_name']}")
                    print(f"      PDF Size: {pdf_info['pdf_size']} bytes")
                    print(f"      Pages: {pdf_info['pages']}")
                    print(f"      Download URL: {pdf_info['download_url']}")
                else:
                    print(f"   ❌ PDF generation failed: {pdf_info}")
            else:
                print(f"   ⚠️ No PDF result in response")
                
        else:
            print(f"❌ Multiple upload failed: {response.status_code}")
            print(f"   Error: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Multiple upload error: {e}")
        return False
    
    # Test 4: Check documents in database
    print("\n📋 Test 4: Check Documents in Database")
    try:
        response = requests.get(f"{base_url}/api/v1/documents")
        
        if response.status_code == 200:
            result = response.json()
            documents = result.get('documents', [])
            summary = result.get('summary', {})
            
            print(f"✅ Retrieved documents from database:")
            print(f"   📊 Total documents: {summary.get('total', 0)}")
            print(f"   🖼️ Images: {summary.get('images', 0)}")
            print(f"   📄 PDFs: {summary.get('pdfs', 0)}")
            
            print(f"\n   📋 Document details:")
            for doc in documents:
                doc_type = "📄 PDF" if doc['type'] == 'pdf' else "🖼️ Image"
                print(f"      {doc_type} ID:{doc['id']} - {doc['name']} ({doc['size']} bytes)")
                
        else:
            print(f"❌ Failed to get documents: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Error getting documents: {e}")
    
    # Test 5: Test images-to-pdf conversion
    print("\n🖼️➡️📄 Test 5: Convert Existing Images to PDF")
    try:
        # Get image document IDs from the previous response
        if 'result' in locals() and 'documents' in result:
            image_docs = [doc for doc in result['documents'] if doc['type'] == 'image']
            
            if len(image_docs) >= 2:
                # Use first 2 images for conversion
                doc_ids = [doc['id'] for doc in image_docs[:2]]
                
                conversion_data = {
                    "document_ids": doc_ids,
                    "pdf_name": "Manual_Conversion_Test",
                    "page_orientation": "portrait"
                }
                
                print(f"   🔄 Converting images with IDs: {doc_ids}")
                
                response = requests.post(f"{base_url}/api/v1/documents/images-to-pdf", 
                                       json=conversion_data)
                
                if response.status_code == 200:
                    result = response.json()
                    print(f"✅ Manual conversion successful!")
                    print(f"   📄 PDF ID: {result['pdf_id']}")
                    print(f"   📝 PDF Name: {result['pdf_name']}")
                    print(f"   💾 PDF Size: {result['pdf_size']} bytes")
                    print(f"   📋 Pages: {result['pages']}")
                    print(f"   🔗 Download URL: {result['download_url']}")
                else:
                    print(f"❌ Manual conversion failed: {response.status_code}")
                    print(f"   Error: {response.text}")
            else:
                print(f"⚠️ Not enough image documents for conversion test")
                
    except Exception as e:
        print(f"❌ Manual conversion error: {e}")
    
    # Test 6: Test PDF download
    print("\n📥 Test 6: Test PDF Download")
    try:
        # Try to download the first generated PDF
        if 'pdf_info' in locals() and pdf_info.get('pdf_name'):
            pdf_filename = pdf_info['pdf_name']
            
            response = requests.get(f"{base_url}/download/{pdf_filename}")
            
            if response.status_code == 200:
                print(f"✅ PDF download successful!")
                print(f"   📄 Downloaded: {pdf_filename}")
                print(f"   💾 Size: {len(response.content)} bytes")
                print(f"   📋 Content-Type: {response.headers.get('content-type', 'unknown')}")
            else:
                print(f"❌ PDF download failed: {response.status_code}")
        else:
            print(f"⚠️ No PDF filename available for download test")
            
    except Exception as e:
        print(f"❌ PDF download error: {e}")
    
    # Cleanup
    print("\n🧹 Test 7: Cleanup")
    for img in test_images:
        try:
            os.unlink(img['path'])
            print(f"   🗑️ Deleted: {img['name']}")
        except:
            pass
    
    # Final summary
    print("\n" + "=" * 60)
    print("🎉 MAIN PROJECT TEST COMPLETED!")
    print("=" * 60)
    
    # Check if uploads directory exists and has files
    uploads_dir = "backend/uploads"
    if os.path.exists(uploads_dir):
        files_in_uploads = os.listdir(uploads_dir)
        pdf_files = [f for f in files_in_uploads if f.endswith('.pdf')]
        image_files = [f for f in files_in_uploads if f.lower().endswith(('.png', '.jpg', '.jpeg'))]
        
        print(f"📁 Files in uploads directory:")
        print(f"   💾 Total files: {len(files_in_uploads)}")
        print(f"   🖼️ Image files: {len(image_files)}")
        print(f"   📄 PDF files: {len(pdf_files)}")
        
        if pdf_files:
            print(f"\n📄 Generated PDFs:")
            for pdf_file in pdf_files:
                file_path = os.path.join(uploads_dir, pdf_file)
                file_size = os.path.getsize(file_path)
                print(f"   • {pdf_file} ({file_size} bytes)")
    
    # Final verdict
    print(f"\n🏆 FINAL VERDICT:")
    if len(pdf_files) > 0:
        print("✅ SUCCESS! Multiple image upload and PDF generation is working!")
        print("✅ Main project functionality confirmed:")
        print("   • Multiple image upload: WORKING")
        print("   • Automatic PDF generation: WORKING")
        print("   • Database storage: WORKING")
        print("   • File download: WORKING")
        return True
    else:
        print("❌ FAILED! No PDFs were generated")
        return False

if __name__ == "__main__":
    success = test_main_project_functionality()
    
    if success:
        print("\n🎉 ALL TESTS PASSED!")
        print("🎯 The main project multiple image upload functionality is working correctly!")
    else:
        print("\n❌ SOME TESTS FAILED!")
        print("🔧 Please check the server logs for errors.")
