import { useState, useCallback } from 'react';
import api from 'services/api';

export interface Document {
  id: number;
  name: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  file_path: string;
  mime_type?: string;
  created_at: string;
}

export const useDocuments = () => {
  const [documents, setDocuments] = useState<Document[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchDocuments = useCallback(async () => {
    setLoading(true);
    try {
      const response = await api.get('/documents');
      setDocuments(response.data);
      setError(null);
    } catch (err) {
      setError('Failed to fetch documents');
    } finally {
      setLoading(false);
    }
  }, []);

  const uploadDocument = useCallback(async (file: File) => {
    const formData = new FormData();
    formData.append('file', file);

    try {
      const response = await api.post('/documents/upload', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      setDocuments(prev => [...prev, response.data]);
      return response.data;
    } catch (err) {
      throw new Error('Failed to upload document');
    }
  }, []);

  const uploadMultipleDocuments = useCallback(async (
    files: File[],
    generatePdf: boolean = false,
    pdfName?: string
  ) => {
    if (!files || files.length === 0) {
      throw new Error('No files selected for upload');
    }

    // Validate file types
    const validTypes = ['image/png', 'image/jpeg', 'image/jpg', 'application/pdf'];
    const invalidFiles = files.filter(file => !validTypes.includes(file.type));

    if (invalidFiles.length > 0) {
      throw new Error(`Invalid file types: ${invalidFiles.map(f => f.name).join(', ')}`);
    }

    const formData = new FormData();

    // Add all files to form data
    files.forEach(file => {
      formData.append('files', file);
    });

    // Add PDF generation options
    if (generatePdf) {
      formData.append('generate_pdf', 'true');
      if (pdfName) {
        formData.append('pdf_name', pdfName);
      }
    }

    try {
      const response = await api.post('/documents/upload-multiple', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
        // Add progress tracking if needed
        onUploadProgress: (progressEvent) => {
          if (progressEvent.total) {
            const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total);
            console.log(`Upload progress: ${percentCompleted}%`);
          }
        }
      });

      // Refresh documents list to get the new uploads
      await fetchDocuments();

      return response.data;
    } catch (err: any) {
      const errorMessage = err.response?.data?.detail || err.message || 'Failed to upload multiple documents';
      throw new Error(errorMessage);
    }
  }, [fetchDocuments]);

  const convertImagesToPdf = useCallback(async (
    documentIds: number[],
    pdfName: string,
    pageOrientation: string = 'portrait',
    imageFit: string = 'contain'
  ) => {
    try {
      const response = await api.post('/documents/images-to-pdf', {
        document_ids: documentIds,
        pdf_name: pdfName,
        page_orientation: pageOrientation,
        image_fit: imageFit
      });

      // Refresh documents list to get the new PDF
      await fetchDocuments();

      return response.data;
    } catch (err) {
      throw new Error('Failed to convert images to PDF');
    }
  }, [fetchDocuments]);

  const deleteDocument = useCallback(async (id: number) => {
    try {
      await api.delete(`/documents/${id}`);
      setDocuments(prev => prev.filter(doc => doc.id !== id));
    } catch (err) {
      throw new Error('Failed to delete document');
    }
  }, []);

  return {
    documents,
    loading,
    error,
    fetchDocuments,
    uploadDocument,
    uploadMultipleDocuments,
    convertImagesToPdf,
    deleteDocument
  };
};
