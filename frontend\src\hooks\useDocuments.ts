import { useState, useCallback } from 'react';
import api from 'services/api';

export interface Document {
  id: number;
  name: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  file_path: string;
  mime_type?: string;
  created_at: string;
}

export const useDocuments = () => {
  const [documents, setDocuments] = useState<Document[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchDocuments = useCallback(async () => {
    setLoading(true);
    try {
      const response = await api.get('/documents');
      setDocuments(response.data);
      setError(null);
    } catch (err) {
      setError('Failed to fetch documents');
    } finally {
      setLoading(false);
    }
  }, []);

  const uploadDocument = useCallback(async (file: File) => {
    const formData = new FormData();
    formData.append('file', file);

    try {
      const response = await api.post('/documents/upload', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      setDocuments(prev => [...prev, response.data]);
      return response.data;
    } catch (err) {
      throw new Error('Failed to upload document');
    }
  }, []);

  const uploadMultipleDocuments = useCallback(async (
    files: File[],
    generatePdf: boolean = false,
    pdfName?: string
  ) => {
    const formData = new FormData();

    files.forEach(file => {
      formData.append('files', file);
    });

    if (generatePdf) {
      formData.append('generate_pdf', 'true');
      if (pdfName) {
        formData.append('pdf_name', pdfName);
      }
    }

    try {
      const response = await api.post('/documents/upload-multiple', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      // Refresh documents list to get the new uploads
      await fetchDocuments();

      return response.data;
    } catch (err) {
      throw new Error('Failed to upload multiple documents');
    }
  }, [fetchDocuments]);

  const convertImagesToPdf = useCallback(async (
    documentIds: number[],
    pdfName: string,
    pageOrientation: string = 'portrait',
    imageFit: string = 'contain'
  ) => {
    try {
      const response = await api.post('/documents/images-to-pdf', {
        document_ids: documentIds,
        pdf_name: pdfName,
        page_orientation: pageOrientation,
        image_fit: imageFit
      });

      // Refresh documents list to get the new PDF
      await fetchDocuments();

      return response.data;
    } catch (err) {
      throw new Error('Failed to convert images to PDF');
    }
  }, [fetchDocuments]);

  const deleteDocument = useCallback(async (id: number) => {
    try {
      await api.delete(`/documents/${id}`);
      setDocuments(prev => prev.filter(doc => doc.id !== id));
    } catch (err) {
      throw new Error('Failed to delete document');
    }
  }, []);

  return {
    documents,
    loading,
    error,
    fetchDocuments,
    uploadDocument,
    uploadMultipleDocuments,
    convertImagesToPdf,
    deleteDocument
  };
};
