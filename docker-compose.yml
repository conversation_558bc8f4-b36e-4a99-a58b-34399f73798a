# ============================================================================
# MULTIPLE IMAGE TO PDF CONVERTER - DOCKER COMPOSE
# ============================================================================

version: '3.8'

services:
  # Main Application
  pdf-converter:
    build: .
    container_name: multiple-image-pdf-converter
    ports:
      - "8001:8001"
    environment:
      - DEBUG=false
      - HOST=0.0.0.0
      - PORT=8001
      - MAX_FILE_SIZE=10485760
      - MAX_FILES_PER_REQUEST=50
    volumes:
      - pdf_storage:/app/uploads
      - app_logs:/app/logs
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "python", "-c", "import requests; requests.get('http://localhost:8001/', timeout=5)"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Nginx Reverse Proxy (Optional)
  nginx:
    image: nginx:alpine
    container_name: pdf-converter-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - pdf-converter
    restart: unless-stopped
    profiles:
      - production

volumes:
  pdf_storage:
    driver: local
  app_logs:
    driver: local

networks:
  default:
    name: pdf-converter-network

# ============================================================================
# USAGE INSTRUCTIONS
# ============================================================================
#
# Development (app only):
# docker-compose up -d pdf-converter
#
# Production (with nginx):
# docker-compose --profile production up -d
#
# View logs:
# docker-compose logs -f pdf-converter
#
# Stop services:
# docker-compose down
#
# Remove volumes (careful - deletes data):
# docker-compose down -v
#
# ============================================================================
