import React, { useState, useEffect } from 'react';
import { useDocuments } from 'hooks/useDocuments';

interface ImageDocument {
  id: number;
  name: string;
  file_path: string;
  mime_type?: string;
}

interface ImagesToPdfConverterProps {
  onConversionComplete?: () => void;
}

export const ImagesToPdfConverter: React.FC<ImagesToPdfConverterProps> = ({ 
  onConversionComplete 
}) => {
  const { documents, convertImagesToPdf } = useDocuments();
  const [selectedImages, setSelectedImages] = useState<number[]>([]);
  const [pdfName, setPdfName] = useState('');
  const [pageOrientation, setPageOrientation] = useState<'portrait' | 'landscape'>('portrait');
  const [imageFit, setImageFit] = useState<'contain' | 'cover' | 'fill'>('contain');
  const [converting, setConverting] = useState(false);
  const [conversionStatus, setConversionStatus] = useState<string | null>(null);

  // Filter documents to show only images
  const imageDocuments = documents.filter(doc => 
    doc.mime_type?.startsWith('image/') || 
    /\.(png|jpg|jpeg|tiff|bmp)$/i.test(doc.name)
  );

  useEffect(() => {
    if (selectedImages.length > 0 && !pdfName) {
      setPdfName(`combined_images_${Date.now()}`);
    }
  }, [selectedImages.length, pdfName]);

  const handleImageSelection = (imageId: number) => {
    setSelectedImages(prev => 
      prev.includes(imageId) 
        ? prev.filter(id => id !== imageId)
        : [...prev, imageId]
    );
  };

  const handleSelectAll = () => {
    if (selectedImages.length === imageDocuments.length) {
      setSelectedImages([]);
    } else {
      setSelectedImages(imageDocuments.map(doc => doc.id));
    }
  };

  const handleConvertToPdf = async () => {
    if (selectedImages.length === 0 || !pdfName.trim()) {
      setConversionStatus('Please select images and enter a PDF name');
      return;
    }

    setConverting(true);
    setConversionStatus(null);

    try {
      await convertImagesToPdf(selectedImages, pdfName.trim(), pageOrientation, imageFit);
      setConversionStatus(`Successfully started PDF generation for ${selectedImages.length} images`);
      setSelectedImages([]);
      setPdfName('');
      
      if (onConversionComplete) {
        onConversionComplete();
      }
    } catch (error) {
      setConversionStatus(`Conversion failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setConverting(false);
    }
  };

  if (imageDocuments.length === 0) {
    return (
      <div className="bg-white p-6 rounded-lg shadow-sm border">
        <h2 className="text-lg font-semibold mb-4">Convert Images to PDF</h2>
        <p className="text-gray-500">No image documents found. Upload some images first.</p>
      </div>
    );
  }

  return (
    <div className="bg-white p-6 rounded-lg shadow-sm border">
      <h2 className="text-lg font-semibold mb-4">Convert Images to PDF</h2>
      
      {/* Image Selection */}
      <div className="mb-6">
        <div className="flex justify-between items-center mb-3">
          <h3 className="font-medium">Select Images ({selectedImages.length} selected)</h3>
          <button
            onClick={handleSelectAll}
            className="text-sm text-blue-600 hover:text-blue-800"
          >
            {selectedImages.length === imageDocuments.length ? 'Deselect All' : 'Select All'}
          </button>
        </div>
        
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3 max-h-60 overflow-y-auto">
          {imageDocuments.map(doc => (
            <div
              key={doc.id}
              className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                selectedImages.includes(doc.id)
                  ? 'border-blue-500 bg-blue-50'
                  : 'border-gray-200 hover:border-gray-300'
              }`}
              onClick={() => handleImageSelection(doc.id)}
            >
              <div className="text-sm font-medium truncate">{doc.name}</div>
              <div className="text-xs text-gray-500 mt-1">{doc.mime_type}</div>
            </div>
          ))}
        </div>
      </div>

      {/* PDF Configuration */}
      {selectedImages.length > 0 && (
        <div className="space-y-4 mb-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              PDF Name
            </label>
            <input
              type="text"
              value={pdfName}
              onChange={(e) => setPdfName(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="Enter PDF name"
            />
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Page Orientation
              </label>
              <select
                value={pageOrientation}
                onChange={(e) => setPageOrientation(e.target.value as 'portrait' | 'landscape')}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="portrait">Portrait</option>
                <option value="landscape">Landscape</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Image Fit
              </label>
              <select
                value={imageFit}
                onChange={(e) => setImageFit(e.target.value as 'contain' | 'cover' | 'fill')}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="contain">Contain (fit within page)</option>
                <option value="cover">Cover (fill page, may crop)</option>
                <option value="fill">Fill (stretch to fit)</option>
              </select>
            </div>
          </div>
        </div>
      )}

      {/* Convert Button */}
      {selectedImages.length > 0 && (
        <div className="flex justify-end">
          <button
            onClick={handleConvertToPdf}
            disabled={converting || !pdfName.trim()}
            className="bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed flex items-center"
          >
            {converting && (
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
            )}
            {converting ? 'Converting...' : `Convert ${selectedImages.length} Images to PDF`}
          </button>
        </div>
      )}

      {/* Status Message */}
      {conversionStatus && (
        <div className={`mt-4 p-4 rounded-md ${
          conversionStatus.includes('failed') || conversionStatus.includes('error')
            ? 'bg-red-50 border border-red-200 text-red-700'
            : 'bg-green-50 border border-green-200 text-green-700'
        }`}>
          {conversionStatus}
        </div>
      )}
    </div>
  );
};
