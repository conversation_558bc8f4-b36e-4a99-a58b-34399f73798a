# ============================================================================
# MULTIPLE IMAGE TO PDF CONVERTER - ENVIRONMENT CONFIGURATION
# ============================================================================

# Application Settings
APP_NAME="Multiple Image to PDF Converter"
APP_VERSION="2.0.0"
DEBUG=false

# Server Configuration
HOST=0.0.0.0
PORT=8001

# Database Configuration
DATABASE_URL=sqlite:///./documents.db

# File Processing Settings
MAX_FILE_SIZE=10485760  # 10MB in bytes
MAX_FILES_PER_REQUEST=50
ALLOWED_EXTENSIONS=.png,.jpg,.jpeg,.webp
UPLOAD_DIR=uploads

# Security Settings
CORS_ORIGINS=http://localhost:3000,http://localhost:8001
TRUSTED_HOSTS=localhost,127.0.0.1

# PDF Generation Settings
PDF_PAGE_SIZE=letter  # letter, a4, legal
PDF_ORIENTATION=portrait  # portrait, landscape
PDF_QUALITY=85  # JPEG quality for PDF optimization (1-100)

# ============================================================================
# USAGE INSTRUCTIONS
# ============================================================================
# 
# 1. Copy this file to .env in the same directory
# 2. Modify the values according to your environment
# 3. The application will automatically load these settings
#
# Example:
# cp .env.example .env
# 
# ============================================================================
