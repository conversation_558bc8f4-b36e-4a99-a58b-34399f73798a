# 🚀 Deployment Guide - Multiple Image to PDF Converter

This guide covers various deployment options for the Multiple Image to PDF Converter application.

## 📋 Prerequisites

- Git
- Docker (recommended) or Python 3.8+
- 2GB+ RAM
- 10GB+ disk space

## 🐳 Docker Deployment (Recommended)

### Quick Start
```bash
# Clone the repository
git clone https://github.com/yourusername/multiple-image-pdf-converter.git
cd multiple-image-pdf-converter

# Build and run with Docker Compose
docker-compose up -d

# Access the application
open http://localhost:8001
```

### Production Deployment
```bash
# Production deployment with Nginx
docker-compose --profile production up -d

# Check status
docker-compose ps

# View logs
docker-compose logs -f pdf-converter
```

## 🐍 Manual Python Deployment

### Backend Setup
```bash
# Clone repository
git clone https://github.com/yourusername/multiple-image-pdf-converter.git
cd multiple-image-pdf-converter/backend

# Create virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt

# Configure environment
cp .env.example .env
# Edit .env with your settings

# Run the application
python multiple_image_demo.py
```

### Frontend Setup (Optional)
```bash
cd frontend

# Install dependencies
npm install

# Development server
npm start

# Production build
npm run build
```

## ☁️ Cloud Deployment

### AWS EC2
```bash
# Launch EC2 instance (t3.medium recommended)
# Install Docker
sudo yum update -y
sudo yum install -y docker
sudo service docker start
sudo usermod -a -G docker ec2-user

# Install Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# Deploy application
git clone https://github.com/yourusername/multiple-image-pdf-converter.git
cd multiple-image-pdf-converter
docker-compose up -d

# Configure security group to allow port 8001
```

### Google Cloud Platform
```bash
# Create VM instance
gcloud compute instances create pdf-converter \
    --image-family=ubuntu-2004-lts \
    --image-project=ubuntu-os-cloud \
    --machine-type=e2-medium \
    --tags=http-server

# SSH into instance
gcloud compute ssh pdf-converter

# Install Docker and deploy
sudo apt update
sudo apt install -y docker.io docker-compose
sudo systemctl start docker
sudo systemctl enable docker
sudo usermod -a -G docker $USER

# Deploy application
git clone https://github.com/yourusername/multiple-image-pdf-converter.git
cd multiple-image-pdf-converter
sudo docker-compose up -d
```

### Azure Container Instances
```bash
# Create resource group
az group create --name pdf-converter-rg --location eastus

# Deploy container
az container create \
    --resource-group pdf-converter-rg \
    --name pdf-converter \
    --image yourusername/multiple-image-pdf-converter:latest \
    --ports 8001 \
    --dns-name-label pdf-converter-app \
    --memory 2 \
    --cpu 1
```

## 🔧 Configuration

### Environment Variables
```bash
# Application
APP_NAME="Multiple Image to PDF Converter"
DEBUG=false

# Server
HOST=0.0.0.0
PORT=8001

# File Processing
MAX_FILE_SIZE=10485760  # 10MB
MAX_FILES_PER_REQUEST=50
PDF_QUALITY=85

# Security
CORS_ORIGINS=https://yourdomain.com,https://www.yourdomain.com
```

### Nginx Configuration (Production)
```nginx
server {
    listen 80;
    server_name yourdomain.com;
    
    location / {
        proxy_pass http://pdf-converter:8001;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # File upload settings
        client_max_body_size 100M;
        proxy_read_timeout 300s;
        proxy_connect_timeout 75s;
    }
}
```

## 📊 Monitoring

### Health Checks
```bash
# Application health
curl http://localhost:8001/

# Docker health
docker-compose ps

# Container logs
docker-compose logs -f pdf-converter
```

### Performance Monitoring
```bash
# Resource usage
docker stats pdf-converter

# Application metrics
curl http://localhost:8001/api/v1/documents
```

## 🔒 Security

### SSL/TLS Setup
```bash
# Generate SSL certificate (Let's Encrypt)
sudo apt install certbot python3-certbot-nginx
sudo certbot --nginx -d yourdomain.com

# Auto-renewal
sudo crontab -e
# Add: 0 12 * * * /usr/bin/certbot renew --quiet
```

### Firewall Configuration
```bash
# Ubuntu/Debian
sudo ufw allow 22/tcp
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw enable

# CentOS/RHEL
sudo firewall-cmd --permanent --add-service=ssh
sudo firewall-cmd --permanent --add-service=http
sudo firewall-cmd --permanent --add-service=https
sudo firewall-cmd --reload
```

## 🔄 Updates

### Application Updates
```bash
# Pull latest changes
git pull origin main

# Rebuild and restart
docker-compose down
docker-compose build --no-cache
docker-compose up -d
```

### Backup
```bash
# Backup generated PDFs
docker run --rm -v pdf-converter_pdf_storage:/data -v $(pwd):/backup alpine tar czf /backup/pdfs-backup.tar.gz /data

# Backup database
docker exec pdf-converter cp documents.db /app/uploads/
```

## 🆘 Troubleshooting

### Common Issues
1. **Port already in use**: Change port in docker-compose.yml
2. **Permission denied**: Check file permissions and user groups
3. **Out of memory**: Increase container memory limits
4. **SSL certificate issues**: Check domain DNS and certificate validity

### Debug Mode
```bash
# Enable debug logging
docker-compose down
docker-compose up pdf-converter -e DEBUG=true

# View detailed logs
docker-compose logs -f pdf-converter
```

## 📞 Support

- **Documentation**: Check README.md
- **Issues**: Create GitHub issue
- **Logs**: Check application logs for detailed error information
- **Health**: Monitor application health endpoints

---

**🎯 Your Multiple Image to PDF Converter is now ready for production deployment!**
