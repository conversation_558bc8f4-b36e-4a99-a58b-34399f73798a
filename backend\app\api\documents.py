from typing import List
from fastapi import APIRouter, Depends, HTTPException, UploadFile, File, Form
from sqlalchemy.orm import Session

from app.database import get_db
from app.models.document import Document
from app.models.user import User
from app.schemas.document import (
    DocumentResponse,
    DocumentUploadResponse,
    BatchUploadResponse,
    ImageToPdfRequest,
    ImageToPdfResponse
)
from app.services.auth_service import get_current_user
from app.services.document_service import save_document, process_document

router = APIRouter()

@router.get("/", response_model=List[DocumentResponse])
async def list_documents(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    documents = db.query(Document).filter(Document.user_id == current_user.id).all()
    return documents

@router.post("/upload", response_model=DocumentUploadResponse)
async def upload_document(
    file: UploadFile = File(...),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    # Get user ID using scalar query to avoid SQLAlchemy Column issues
    user_id = db.query(User.id).filter(User.email == current_user.email).scalar()

    document = await save_document(file, user_id, db)

    # Get the document ID from the database after refresh
    doc_id = db.query(Document.id).filter(Document.file_path == document.file_path).scalar()

    return DocumentUploadResponse(
        id=doc_id or 1,
        name=file.filename or "unknown",
        status="pending",
        message="Document uploaded successfully"
    )


@router.post("/upload-multiple", response_model=BatchUploadResponse)
async def upload_multiple_documents(
    files: List[UploadFile] = File(...),
    generate_pdf: bool = Form(False),
    pdf_name: str = Form(None),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Upload multiple files and optionally generate PDF from images."""
    # Get user ID
    user_id = db.query(User.id).filter(User.email == current_user.email).scalar()

    uploaded_files = []
    uploaded_document_ids = []

    # Upload all files
    for file in files:
        try:
            document = await save_document(file, user_id, db)
            doc_id = db.query(Document.id).filter(Document.file_path == document.file_path).scalar()

            uploaded_files.append(DocumentUploadResponse(
                id=doc_id or 1,
                name=file.filename or "unknown",
                status="pending",
                message="Document uploaded successfully"
            ))

            if doc_id:
                uploaded_document_ids.append(doc_id)

        except Exception as e:
            uploaded_files.append(DocumentUploadResponse(
                id=0,
                name=file.filename or "unknown",
                status="failed",
                message=f"Upload failed: {str(e)}"
            ))

    # Generate PDF if requested and we have uploaded images
    pdf_generation_status = None
    batch_id = None

    if generate_pdf and uploaded_document_ids and pdf_name:
        try:
            # Import here to avoid circular imports
            from app.services.pdf_service import generate_pdf_from_images

            # Start PDF generation task
            task = generate_pdf_from_images.delay(
                document_ids=uploaded_document_ids,
                pdf_name=pdf_name,
                user_id=user_id
            )
            batch_id = task.id
            pdf_generation_status = "processing"
        except Exception as e:
            pdf_generation_status = f"failed: {str(e)}"

    return BatchUploadResponse(
        uploaded_files=uploaded_files,
        batch_id=batch_id,
        pdf_generation_status=pdf_generation_status,
        message=f"Uploaded {len(uploaded_files)} files successfully"
    )

@router.get("/{document_id}", response_model=DocumentResponse)
async def get_document(
    document_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    document = db.query(Document).filter(
        Document.id == document_id,
        Document.user_id == current_user.id
    ).first()

    if not document:
        raise HTTPException(status_code=404, detail="Document not found")

    return document


@router.post("/{document_id}/process")
async def process_document_endpoint(
    document_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Manually trigger document processing for testing."""
    from app.services.document_service import process_document

    # Check if document exists and belongs to user
    document = db.query(Document).filter(
        Document.id == document_id,
        Document.user_id == current_user.id
    ).first()

    if not document:
        raise HTTPException(status_code=404, detail="Document not found")

    # Process the document synchronously for testing
    try:
        result = process_document(document_id)
        return {"message": "Document processed", "result": result}
    except Exception as e:
        return {"error": str(e)}


@router.delete("/{document_id}")
async def delete_document(
    document_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    document = db.query(Document).filter(
        Document.id == document_id,
        Document.user_id == current_user.id
    ).first()
    
    if not document:
        raise HTTPException(status_code=404, detail="Document not found")
    
    db.delete(document)
    db.commit()
    return {"message": "Document deleted successfully"}


@router.post("/images-to-pdf", response_model=ImageToPdfResponse)
async def convert_images_to_pdf(
    request: ImageToPdfRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Convert multiple images to a single PDF."""
    # Get user ID
    user_id = db.query(User.id).filter(User.email == current_user.email).scalar()

    # Validate that all documents belong to the user
    user_documents = db.query(Document.id).filter(
        Document.id.in_(request.document_ids),
        Document.user_id == user_id
    ).all()

    if len(user_documents) != len(request.document_ids):
        raise HTTPException(
            status_code=404,
            detail="Some documents not found or don't belong to user"
        )

    try:
        # Import here to avoid circular imports
        from app.services.pdf_service import generate_pdf_from_images

        # Start PDF generation task
        task = generate_pdf_from_images.delay(
            document_ids=request.document_ids,
            pdf_name=request.pdf_name,
            page_orientation=request.page_orientation,
            image_fit=request.image_fit,
            user_id=user_id
        )

        return ImageToPdfResponse(
            pdf_document_id=0,  # Will be updated when task completes
            pdf_name=request.pdf_name,
            status="processing",
            message=f"PDF generation started for {len(request.document_ids)} images"
        )

    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to start PDF generation: {str(e)}"
        )
