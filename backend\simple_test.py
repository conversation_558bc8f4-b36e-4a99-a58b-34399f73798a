"""
Simple test to verify API endpoints are working
"""

import requests
from PIL import Image
import tempfile
import os

def create_simple_test_image():
    """Create a simple test image."""
    tmp_file = tempfile.NamedTemporaryFile(suffix='_simple_test.png', delete=False)
    tmp_file.close()
    
    # Create a simple red image
    img = Image.new('RGB', (200, 200), color='red')
    img.save(tmp_file.name)
    
    print(f"✅ Created test image: {tmp_file.name}")
    return tmp_file.name

def test_endpoints():
    """Test all endpoints."""
    base_url = "http://localhost:8000"
    
    # Test 1: Root endpoint
    print("🧪 Testing root endpoint...")
    try:
        response = requests.get(f"{base_url}/")
        print(f"✅ Root endpoint: {response.status_code} - {response.json()}")
    except Exception as e:
        print(f"❌ Root endpoint failed: {e}")
        return False
    
    # Test 2: Documents endpoint
    print("\n🧪 Testing documents endpoint...")
    try:
        response = requests.get(f"{base_url}/documents")
        print(f"✅ Documents endpoint: {response.status_code} - {response.json()}")
    except Exception as e:
        print(f"❌ Documents endpoint failed: {e}")
    
    # Test 3: Single upload
    print("\n🧪 Testing single upload...")
    image_path = create_simple_test_image()
    
    try:
        with open(image_path, 'rb') as f:
            files = {'file': ('test.png', f, 'image/png')}
            response = requests.post(f"{base_url}/upload", files=files)
        
        print(f"✅ Single upload: {response.status_code} - {response.json()}")
        
        # Clean up
        os.unlink(image_path)
        
    except Exception as e:
        print(f"❌ Single upload failed: {e}")
        try:
            os.unlink(image_path)
        except:
            pass
    
    # Test 4: Multiple upload with PDF
    print("\n🧪 Testing multiple upload with PDF generation...")
    
    # Create multiple test images
    image_paths = []
    for i in range(3):
        tmp_file = tempfile.NamedTemporaryFile(suffix=f'_multi_test_{i}.png', delete=False)
        tmp_file.close()
        
        colors = ['red', 'green', 'blue']
        img = Image.new('RGB', (200 + i*50, 200 + i*50), color=colors[i])
        img.save(tmp_file.name)
        image_paths.append(tmp_file.name)
    
    try:
        files = []
        for i, img_path in enumerate(image_paths):
            with open(img_path, 'rb') as f:
                files.append(('files', (f'test_{i}.png', f.read(), 'image/png')))
        
        data = {
            'generate_pdf': 'true',
            'pdf_name': 'Simple_Test_PDF'
        }
        
        response = requests.post(f"{base_url}/upload-multiple", files=files, data=data)
        print(f"✅ Multiple upload: {response.status_code} - {response.json()}")
        
    except Exception as e:
        print(f"❌ Multiple upload failed: {e}")
    finally:
        # Clean up
        for path in image_paths:
            try:
                os.unlink(path)
            except:
                pass
    
    # Test 5: Check final documents
    print("\n🧪 Checking final documents...")
    try:
        response = requests.get(f"{base_url}/documents")
        result = response.json()
        documents = result.get('documents', [])
        
        print(f"✅ Final documents count: {len(documents)}")
        for doc in documents:
            print(f"   📄 {doc['name']} (ID: {doc['id']}, Type: {doc['mime_type']})")
        
        # Check if we have PDFs
        pdfs = [doc for doc in documents if doc['mime_type'] == 'application/pdf']
        if pdfs:
            print(f"\n🎉 SUCCESS! Generated {len(pdfs)} PDF(s):")
            for pdf in pdfs:
                print(f"   📄 {pdf['name']} (Size: {pdf['size']} bytes)")
        else:
            print("\n⚠️ No PDFs found")
            
    except Exception as e:
        print(f"❌ Final documents check failed: {e}")

if __name__ == "__main__":
    print("🚀 Running Simple API Test")
    print("=" * 40)
    test_endpoints()
    print("\n🏁 Test Complete!")
