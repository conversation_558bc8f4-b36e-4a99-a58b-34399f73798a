# ============================================================================
# MULTIPLE IMAGE TO PDF CONVERTER - PRODUCTION DOCKERFILE
# ============================================================================

FROM python:3.11-slim

# Set metadata
LABEL maintainer="Document Processing Platform Team"
LABEL description="Multiple Image to PDF Converter - Production Ready"
LABEL version="2.0.0"

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1
ENV DEBIAN_FRONTEND=noninteractive

# Set work directory
WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    libc6-dev \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements first for better caching
COPY backend/requirements.txt /app/requirements.txt

# Install Python dependencies
RUN pip install --no-cache-dir --upgrade pip && \
    pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY backend/ /app/

# Create uploads directory
RUN mkdir -p /app/uploads

# Create non-root user for security
RUN useradd --create-home --shell /bin/bash appuser && \
    chown -R appuser:appuser /app
USER appuser

# Expose port
EXPOSE 8001

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD python -c "import requests; requests.get('http://localhost:8001/', timeout=5)" || exit 1

# Run the application
CMD ["python", "multiple_image_demo.py"]

# ============================================================================
# BUILD INSTRUCTIONS
# ============================================================================
#
# Build the image:
# docker build -t multiple-image-pdf-converter:latest .
#
# Run the container:
# docker run -d -p 8001:8001 --name pdf-converter multiple-image-pdf-converter:latest
#
# Access the application:
# http://localhost:8001
#
# ============================================================================
