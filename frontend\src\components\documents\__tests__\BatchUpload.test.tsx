import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { BatchUpload } from '../BatchUpload';
import { useDocuments } from '../../../hooks/useDocuments';

// Mock the useDocuments hook
jest.mock('../../../hooks/useDocuments');
const mockUseDocuments = useDocuments as jest.MockedFunction<typeof useDocuments>;

// Mock UploadZone component
jest.mock('../UploadZone', () => ({
  UploadZone: ({ onMultipleUpload }: any) => (
    <div data-testid="upload-zone">
      <button 
        onClick={() => onMultipleUpload && onMultipleUpload([
          new File(['test1'], 'test1.png', { type: 'image/png' }),
          new File(['test2'], 'test2.jpg', { type: 'image/jpeg' })
        ], true, 'test_pdf')}
        data-testid="mock-upload-button"
      >
        Mock Upload
      </button>
    </div>
  )
}));

describe('BatchUpload', () => {
  const mockUploadMultipleDocuments = jest.fn();
  const mockOnUploadComplete = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
    mockUseDocuments.mockReturnValue({
      documents: [],
      loading: false,
      error: null,
      fetchDocuments: jest.fn(),
      uploadDocument: jest.fn(),
      uploadMultipleDocuments: mockUploadMultipleDocuments,
      convertImagesToPdf: jest.fn(),
      deleteDocument: jest.fn()
    });
  });

  it('renders batch upload component', () => {
    render(<BatchUpload />);
    
    expect(screen.getByText('Upload Documents')).toBeInTheDocument();
    expect(screen.getByTestId('upload-zone')).toBeInTheDocument();
    expect(screen.getByText('Features:')).toBeInTheDocument();
    expect(screen.getByText('• Upload multiple files at once')).toBeInTheDocument();
  });

  it('shows loading state during upload', async () => {
    mockUploadMultipleDocuments.mockImplementation(() => 
      new Promise(resolve => setTimeout(resolve, 100))
    );

    render(<BatchUpload />);
    
    const uploadButton = screen.getByTestId('mock-upload-button');
    fireEvent.click(uploadButton);

    await waitFor(() => {
      expect(screen.getByText('Uploading files...')).toBeInTheDocument();
    });
  });

  it('handles successful upload', async () => {
    mockUploadMultipleDocuments.mockResolvedValue({
      uploaded_files: [
        { id: 1, name: 'test1.png', status: 'pending', message: 'Success' },
        { id: 2, name: 'test2.jpg', status: 'pending', message: 'Success' }
      ],
      pdf_generation_status: 'processing',
      message: 'Upload successful'
    });

    render(<BatchUpload onUploadComplete={mockOnUploadComplete} />);
    
    const uploadButton = screen.getByTestId('mock-upload-button');
    fireEvent.click(uploadButton);

    await waitFor(() => {
      expect(screen.getByText(/Uploaded 2 files. PDF generation: processing/)).toBeInTheDocument();
    });

    expect(mockOnUploadComplete).toHaveBeenCalled();
  });

  it('handles upload without PDF generation', async () => {
    mockUploadMultipleDocuments.mockResolvedValue({
      uploaded_files: [
        { id: 1, name: 'test1.pdf', status: 'pending', message: 'Success' }
      ],
      pdf_generation_status: null,
      message: 'Upload successful'
    });

    render(<BatchUpload />);
    
    const uploadButton = screen.getByTestId('mock-upload-button');
    fireEvent.click(uploadButton);

    await waitFor(() => {
      expect(screen.getByText('Successfully uploaded 1 files')).toBeInTheDocument();
    });
  });

  it('handles upload error', async () => {
    mockUploadMultipleDocuments.mockRejectedValue(new Error('Upload failed'));

    render(<BatchUpload />);
    
    const uploadButton = screen.getByTestId('mock-upload-button');
    fireEvent.click(uploadButton);

    await waitFor(() => {
      expect(screen.getByText('Upload failed: Upload failed')).toBeInTheDocument();
    });
  });

  it('displays features list correctly', () => {
    render(<BatchUpload />);
    
    expect(screen.getByText('• Upload multiple files at once')).toBeInTheDocument();
    expect(screen.getByText('• Automatically combine multiple images into a single PDF')).toBeInTheDocument();
    expect(screen.getByText('• Support for folders (select multiple files from folder)')).toBeInTheDocument();
    expect(screen.getByText('• Individual file processing and OCR')).toBeInTheDocument();
    expect(screen.getByText('• Batch operations for efficiency')).toBeInTheDocument();
  });

  it('clears status after successful upload', async () => {
    mockUploadMultipleDocuments.mockResolvedValue({
      uploaded_files: [{ id: 1, name: 'test.png', status: 'pending', message: 'Success' }],
      pdf_generation_status: null,
      message: 'Upload successful'
    });

    render(<BatchUpload />);
    
    const uploadButton = screen.getByTestId('mock-upload-button');
    fireEvent.click(uploadButton);

    // Wait for upload to complete
    await waitFor(() => {
      expect(screen.getByText('Successfully uploaded 1 files')).toBeInTheDocument();
    });

    // Loading should be false
    expect(screen.queryByText('Uploading files...')).not.toBeInTheDocument();
  });
});
