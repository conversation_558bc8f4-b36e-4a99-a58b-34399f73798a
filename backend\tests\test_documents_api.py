import pytest
from fastapi.testclient import TestClient
from unittest.mock import Mock, patch, AsyncMock
import io
from PIL import Image

from app.main import app
from app.models.document import Document
from app.models.user import User


class TestDocumentsAPI:
    
    def setup_method(self):
        """Setup test client and mocks."""
        self.client = TestClient(app)
        
        # Mock authentication
        self.mock_user = Mock()
        self.mock_user.id = 1
        self.mock_user.email = "<EMAIL>"
    
    def create_test_image_file(self, filename="test.png", size=(100, 100)):
        """Create a test image file in memory."""
        img = Image.new('RGB', size, color='red')
        img_bytes = io.BytesIO()
        img.save(img_bytes, format='PNG')
        img_bytes.seek(0)
        return img_bytes
    
    @patch('app.api.documents.get_current_user')
    @patch('app.api.documents.get_db')
    @patch('app.api.documents.save_document')
    def test_upload_single_document_success(self, mock_save_doc, mock_get_db, mock_get_user):
        """Test successful single document upload."""
        # Setup mocks
        mock_get_user.return_value = self.mock_user
        mock_db = Mock()
        mock_get_db.return_value = mock_db
        mock_db.query.return_value.filter.return_value.scalar.return_value = 1
        
        mock_document = Mock()
        mock_document.file_path = "/path/to/test.png"
        mock_save_doc.return_value = mock_document
        mock_db.query.return_value.filter.return_value.scalar.return_value = 123
        
        # Create test file
        test_file = self.create_test_image_file()
        
        # Make request
        response = self.client.post(
            "/api/v1/documents/upload",
            files={"file": ("test.png", test_file, "image/png")}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["name"] == "test.png"
        assert data["status"] == "pending"
        assert "successfully" in data["message"]
    
    @patch('app.api.documents.get_current_user')
    @patch('app.api.documents.get_db')
    @patch('app.api.documents.save_document')
    def test_upload_multiple_documents_success(self, mock_save_doc, mock_get_db, mock_get_user):
        """Test successful multiple document upload."""
        # Setup mocks
        mock_get_user.return_value = self.mock_user
        mock_db = Mock()
        mock_get_db.return_value = mock_db
        mock_db.query.return_value.filter.return_value.scalar.return_value = 1
        
        # Mock document creation
        mock_document1 = Mock()
        mock_document1.file_path = "/path/to/test1.png"
        mock_document2 = Mock()
        mock_document2.file_path = "/path/to/test2.png"
        
        mock_save_doc.side_effect = [mock_document1, mock_document2]
        mock_db.query.return_value.filter.return_value.scalar.side_effect = [123, 124]
        
        # Create test files
        test_file1 = self.create_test_image_file()
        test_file2 = self.create_test_image_file()
        
        # Make request
        response = self.client.post(
            "/api/v1/documents/upload-multiple",
            files=[
                ("files", ("test1.png", test_file1, "image/png")),
                ("files", ("test2.png", test_file2, "image/png"))
            ],
            data={"generate_pdf": "false"}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert len(data["uploaded_files"]) == 2
        assert "2 files successfully" in data["message"]
    
    @patch('app.api.documents.get_current_user')
    @patch('app.api.documents.get_db')
    @patch('app.api.documents.save_document')
    def test_upload_multiple_with_pdf_generation(self, mock_save_doc, mock_get_db, mock_get_user):
        """Test multiple upload with PDF generation."""
        # Setup mocks
        mock_get_user.return_value = self.mock_user
        mock_db = Mock()
        mock_get_db.return_value = mock_db
        mock_db.query.return_value.filter.return_value.scalar.return_value = 1
        
        # Mock document creation
        mock_document = Mock()
        mock_document.file_path = "/path/to/test.png"
        mock_save_doc.return_value = mock_document
        mock_db.query.return_value.filter.return_value.scalar.return_value = 123
        
        # Mock PDF generation task
        with patch('app.services.pdf_service.generate_pdf_from_images') as mock_pdf_task:
            mock_task = Mock()
            mock_task.id = "task-123"
            mock_pdf_task.delay.return_value = mock_task
            
            # Create test file
            test_file = self.create_test_image_file()
            
            # Make request
            response = self.client.post(
                "/api/v1/documents/upload-multiple",
                files=[("files", ("test.png", test_file, "image/png"))],
                data={
                    "generate_pdf": "true",
                    "pdf_name": "combined_images"
                }
            )
            
            assert response.status_code == 200
            data = response.json()
            assert data["pdf_generation_status"] == "processing"
            assert data["batch_id"] == "task-123"
    
    @patch('app.api.documents.get_current_user')
    @patch('app.api.documents.get_db')
    def test_convert_images_to_pdf_success(self, mock_get_db, mock_get_user):
        """Test successful image to PDF conversion."""
        # Setup mocks
        mock_get_user.return_value = self.mock_user
        mock_db = Mock()
        mock_get_db.return_value = mock_db
        mock_db.query.return_value.filter.return_value.scalar.return_value = 1
        
        # Mock user documents validation
        mock_db.query.return_value.filter.return_value.all.return_value = [
            Mock(id=1), Mock(id=2)
        ]
        
        # Mock PDF generation task
        with patch('app.services.pdf_service.generate_pdf_from_images') as mock_pdf_task:
            mock_task = Mock()
            mock_task.id = "task-456"
            mock_pdf_task.delay.return_value = mock_task
            
            # Make request
            response = self.client.post(
                "/api/v1/documents/images-to-pdf",
                json={
                    "document_ids": [1, 2],
                    "pdf_name": "converted_images",
                    "page_orientation": "portrait",
                    "image_fit": "contain"
                }
            )
            
            assert response.status_code == 200
            data = response.json()
            assert data["pdf_name"] == "converted_images"
            assert data["status"] == "processing"
            assert "2 images" in data["message"]
    
    @patch('app.api.documents.get_current_user')
    @patch('app.api.documents.get_db')
    def test_convert_images_to_pdf_invalid_documents(self, mock_get_db, mock_get_user):
        """Test image to PDF conversion with invalid document IDs."""
        # Setup mocks
        mock_get_user.return_value = self.mock_user
        mock_db = Mock()
        mock_get_db.return_value = mock_db
        mock_db.query.return_value.filter.return_value.scalar.return_value = 1
        
        # Mock user documents validation - only 1 document found instead of 2
        mock_db.query.return_value.filter.return_value.all.return_value = [Mock(id=1)]
        
        # Make request
        response = self.client.post(
            "/api/v1/documents/images-to-pdf",
            json={
                "document_ids": [1, 999],  # 999 doesn't exist
                "pdf_name": "converted_images",
                "page_orientation": "portrait",
                "image_fit": "contain"
            }
        )
        
        assert response.status_code == 404
        assert "not found" in response.json()["detail"]
    
    @patch('app.api.documents.get_current_user')
    @patch('app.api.documents.get_db')
    def test_get_document_success(self, mock_get_db, mock_get_user):
        """Test successful document retrieval."""
        # Setup mocks
        mock_get_user.return_value = self.mock_user
        mock_db = Mock()
        mock_get_db.return_value = mock_db
        
        mock_document = Mock()
        mock_document.id = 1
        mock_document.name = "test.png"
        mock_document.status = "completed"
        mock_db.query.return_value.filter.return_value.first.return_value = mock_document
        
        # Make request
        response = self.client.get("/api/v1/documents/1")
        
        assert response.status_code == 200
        # Note: Actual response would depend on DocumentResponse schema
    
    @patch('app.api.documents.get_current_user')
    @patch('app.api.documents.get_db')
    def test_delete_document_success(self, mock_get_db, mock_get_user):
        """Test successful document deletion."""
        # Setup mocks
        mock_get_user.return_value = self.mock_user
        mock_db = Mock()
        mock_get_db.return_value = mock_db
        
        mock_document = Mock()
        mock_document.id = 1
        mock_db.query.return_value.filter.return_value.first.return_value = mock_document
        
        # Make request
        response = self.client.delete("/api/v1/documents/1")
        
        assert response.status_code == 200
        data = response.json()
        assert "deleted successfully" in data["message"]


if __name__ == "__main__":
    pytest.main([__file__])
