<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Multiple Image Upload Demo</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .upload-area {
            border: 3px dashed #007bff;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            margin: 20px 0;
            background-color: #f8f9fa;
            transition: all 0.3s ease;
        }
        .upload-area:hover {
            border-color: #0056b3;
            background-color: #e3f2fd;
        }
        .upload-area.dragover {
            border-color: #28a745;
            background-color: #d4edda;
        }
        .file-input {
            display: none;
        }
        .upload-btn {
            background-color: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
        }
        .upload-btn:hover {
            background-color: #0056b3;
        }
        .generate-pdf-btn {
            background-color: #28a745;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
        }
        .generate-pdf-btn:hover {
            background-color: #1e7e34;
        }
        .file-list {
            margin: 20px 0;
            padding: 20px;
            background-color: #f8f9fa;
            border-radius: 5px;
        }
        .file-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            margin: 5px 0;
            background: white;
            border-radius: 5px;
            border: 1px solid #dee2e6;
        }
        .file-info {
            display: flex;
            align-items: center;
        }
        .file-icon {
            margin-right: 10px;
            font-size: 20px;
        }
        .remove-btn {
            background-color: #dc3545;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 3px;
            cursor: pointer;
        }
        .remove-btn:hover {
            background-color: #c82333;
        }
        .options {
            margin: 20px 0;
            padding: 20px;
            background-color: #e9ecef;
            border-radius: 5px;
        }
        .option-group {
            margin: 10px 0;
        }
        .option-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .option-group input, .option-group select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ced4da;
            border-radius: 4px;
        }
        .checkbox-group {
            display: flex;
            align-items: center;
        }
        .checkbox-group input {
            width: auto;
            margin-right: 10px;
        }
        .result {
            margin: 20px 0;
            padding: 20px;
            border-radius: 5px;
            display: none;
        }
        .result.success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .result.error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .progress {
            width: 100%;
            height: 20px;
            background-color: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
            display: none;
        }
        .progress-bar {
            height: 100%;
            background-color: #007bff;
            width: 0%;
            transition: width 0.3s ease;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🖼️➡️📄 Multiple Image Upload & PDF Generation Demo</h1>
        <p><strong>Select multiple images at once and generate PDF automatically!</strong></p>
        
        <div class="upload-area" id="uploadArea">
            <h3>📁 Drop multiple images here or click to select</h3>
            <p>Supports: PNG, JPG, JPEG</p>
            <p><strong>💡 Tip: Hold Ctrl (Windows) or Cmd (Mac) to select multiple files at once!</strong></p>
            <button class="upload-btn" onclick="document.getElementById('fileInput').click()">
                📂 Choose Multiple Images
            </button>
            <input type="file" id="fileInput" class="file-input" multiple accept="image/*">
        </div>

        <div class="file-list" id="fileList" style="display: none;">
            <h3>📋 Selected Images:</h3>
            <div id="selectedFiles"></div>
        </div>

        <div class="options">
            <h3>⚙️ PDF Generation Options</h3>
            <div class="checkbox-group">
                <input type="checkbox" id="generatePdf" checked>
                <label for="generatePdf">Generate PDF from uploaded images</label>
            </div>
            <div class="option-group">
                <label for="pdfName">PDF Name:</label>
                <input type="text" id="pdfName" value="My_Combined_Images" placeholder="Enter PDF name">
            </div>
        </div>

        <div class="progress" id="progressBar">
            <div class="progress-bar" id="progressBarFill"></div>
        </div>

        <button class="generate-pdf-btn" id="uploadBtn" onclick="uploadFiles()" disabled>
            🚀 Upload Images & Generate PDF
        </button>

        <div class="result" id="result"></div>
    </div>

    <script>
        let selectedFiles = [];

        // File input change handler
        document.getElementById('fileInput').addEventListener('change', function(e) {
            handleFiles(e.target.files);
        });

        // Drag and drop handlers
        const uploadArea = document.getElementById('uploadArea');
        
        uploadArea.addEventListener('dragover', function(e) {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });

        uploadArea.addEventListener('dragleave', function(e) {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
        });

        uploadArea.addEventListener('drop', function(e) {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            handleFiles(e.dataTransfer.files);
        });

        function handleFiles(files) {
            selectedFiles = Array.from(files).filter(file => file.type.startsWith('image/'));
            displaySelectedFiles();
            updateUploadButton();
        }

        function displaySelectedFiles() {
            const fileList = document.getElementById('fileList');
            const selectedFilesDiv = document.getElementById('selectedFiles');
            
            if (selectedFiles.length === 0) {
                fileList.style.display = 'none';
                return;
            }

            fileList.style.display = 'block';
            selectedFilesDiv.innerHTML = '';

            selectedFiles.forEach((file, index) => {
                const fileItem = document.createElement('div');
                fileItem.className = 'file-item';
                fileItem.innerHTML = `
                    <div class="file-info">
                        <span class="file-icon">🖼️</span>
                        <div>
                            <strong>${file.name}</strong><br>
                            <small>${(file.size / 1024).toFixed(1)} KB</small>
                        </div>
                    </div>
                    <button class="remove-btn" onclick="removeFile(${index})">❌</button>
                `;
                selectedFilesDiv.appendChild(fileItem);
            });
        }

        function removeFile(index) {
            selectedFiles.splice(index, 1);
            displaySelectedFiles();
            updateUploadButton();
        }

        function updateUploadButton() {
            const uploadBtn = document.getElementById('uploadBtn');
            uploadBtn.disabled = selectedFiles.length === 0;
            uploadBtn.textContent = selectedFiles.length > 0 
                ? `🚀 Upload ${selectedFiles.length} Images & Generate PDF`
                : '🚀 Upload Images & Generate PDF';
        }

        async function uploadFiles() {
            if (selectedFiles.length === 0) {
                showResult('Please select at least one image file.', 'error');
                return;
            }

            const formData = new FormData();
            
            // Add all selected files
            selectedFiles.forEach(file => {
                formData.append('files', file);
            });

            // Add options
            const generatePdf = document.getElementById('generatePdf').checked;
            const pdfName = document.getElementById('pdfName').value || 'Combined_Images';
            
            formData.append('generate_pdf', generatePdf);
            formData.append('pdf_name', pdfName);

            // Show progress
            const progressBar = document.getElementById('progressBar');
            const progressBarFill = document.getElementById('progressBarFill');
            progressBar.style.display = 'block';
            progressBarFill.style.width = '0%';

            try {
                // Simulate progress
                let progress = 0;
                const progressInterval = setInterval(() => {
                    progress += 10;
                    progressBarFill.style.width = progress + '%';
                    if (progress >= 90) {
                        clearInterval(progressInterval);
                    }
                }, 100);

                const response = await fetch('http://localhost:8001/upload-multiple', {
                    method: 'POST',
                    body: formData
                });

                clearInterval(progressInterval);
                progressBarFill.style.width = '100%';

                if (response.ok) {
                    const result = await response.json();
                    
                    let message = `✅ Successfully uploaded ${result.uploaded_count} images!`;
                    
                    if (result.pdf_result && result.pdf_result.status === 'generated') {
                        message += `\n📄 PDF generated: ${result.pdf_result.pdf_name}`;
                        message += `\n💾 Size: ${result.pdf_result.pdf_size} bytes`;
                        message += `\n📋 Pages: ${result.pdf_result.pages}`;
                        message += `\n🔗 Download: ${result.pdf_result.download_url}`;
                    }
                    
                    showResult(message, 'success');
                    
                    // Reset form
                    selectedFiles = [];
                    displaySelectedFiles();
                    updateUploadButton();
                    document.getElementById('fileInput').value = '';
                    
                } else {
                    const error = await response.text();
                    showResult(`❌ Upload failed: ${error}`, 'error');
                }

            } catch (error) {
                showResult(`❌ Network error: ${error.message}`, 'error');
            } finally {
                setTimeout(() => {
                    progressBar.style.display = 'none';
                }, 1000);
            }
        }

        function showResult(message, type) {
            const result = document.getElementById('result');
            result.textContent = message;
            result.className = `result ${type}`;
            result.style.display = 'block';
            
            // Auto-hide after 10 seconds
            setTimeout(() => {
                result.style.display = 'none';
            }, 10000);
        }

        // Initialize
        updateUploadButton();
    </script>
</body>
</html>
