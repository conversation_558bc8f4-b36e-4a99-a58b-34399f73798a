"""
Test script to verify the API functionality by making actual API calls
"""

import requests
import os
from PIL import Image
import tempfile
import json

# Server URL
BASE_URL = "http://localhost:8000"

def create_test_images(count=3):
    """Create test images for testing."""
    images = []
    colors = ['red', 'green', 'blue', 'yellow', 'purple']
    
    for i in range(count):
        # Create temporary file
        tmp_file = tempfile.NamedTemporaryFile(suffix=f'_test_{i}.png', delete=False)
        tmp_file.close()
        
        # Create test image with different sizes and colors
        width = 400 + (i * 100)
        height = 300 + (i * 50)
        color = colors[i % len(colors)]
        
        img = Image.new('RGB', (width, height), color=color)
        img.save(tmp_file.name)
        
        images.append({
            'path': tmp_file.name,
            'width': width,
            'height': height,
            'color': color,
            'name': f'test_image_{i}.png'
        })
        
        print(f"✅ Created test image: {tmp_file.name} ({width}x{height}, {color})")
    
    return images

def test_server_status():
    """Test if server is running."""
    try:
        response = requests.get(f"{BASE_URL}/")
        if response.status_code == 200:
            print("✅ Server is running")
            print(f"📋 Server response: {response.json()}")
            return True
        else:
            print(f"❌ Server returned status code: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Cannot connect to server: {e}")
        return False

def test_single_upload(image_path, image_name):
    """Test single image upload."""
    try:
        with open(image_path, 'rb') as f:
            files = {'file': (image_name, f, 'image/png')}
            response = requests.post(f"{BASE_URL}/upload", files=files)
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Single upload successful: {result}")
            return result
        else:
            print(f"❌ Single upload failed: {response.status_code} - {response.text}")
            return None
    except Exception as e:
        print(f"❌ Single upload error: {e}")
        return None

def test_multiple_upload_with_pdf(image_paths, pdf_name="Test_Combined_PDF"):
    """Test multiple image upload with PDF generation."""
    try:
        files = []
        for i, img_path in enumerate(image_paths):
            with open(img_path, 'rb') as f:
                files.append(('files', (f'test_image_{i}.png', f.read(), 'image/png')))
        
        data = {
            'generate_pdf': 'true',
            'pdf_name': pdf_name
        }
        
        response = requests.post(f"{BASE_URL}/upload-multiple", files=files, data=data)
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Multiple upload with PDF successful:")
            print(f"   📁 Uploaded files: {len(result['uploaded_files'])}")
            print(f"   📄 PDF generation status: {result.get('pdf_generation_status')}")
            print(f"   🆔 Batch ID: {result.get('batch_id')}")
            return result
        else:
            print(f"❌ Multiple upload failed: {response.status_code} - {response.text}")
            return None
    except Exception as e:
        print(f"❌ Multiple upload error: {e}")
        return None

def test_images_to_pdf_conversion(document_ids, pdf_name="Manual_Conversion_Test"):
    """Test converting existing images to PDF."""
    try:
        data = {
            "document_ids": document_ids,
            "pdf_name": pdf_name,
            "page_orientation": "portrait",
            "image_fit": "contain"
        }
        
        response = requests.post(f"{BASE_URL}/images-to-pdf", json=data)
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Images to PDF conversion successful:")
            print(f"   📄 PDF Document ID: {result['pdf_document_id']}")
            print(f"   📝 PDF Name: {result['pdf_name']}")
            print(f"   ✅ Status: {result['status']}")
            print(f"   💬 Message: {result['message']}")
            return result
        else:
            print(f"❌ Images to PDF conversion failed: {response.status_code} - {response.text}")
            return None
    except Exception as e:
        print(f"❌ Images to PDF conversion error: {e}")
        return None

def test_get_documents():
    """Test getting all documents."""
    try:
        response = requests.get(f"{BASE_URL}/documents")
        
        if response.status_code == 200:
            result = response.json()
            documents = result.get('documents', [])
            print(f"✅ Retrieved {len(documents)} documents:")
            for doc in documents:
                print(f"   🆔 ID: {doc['id']} | 📝 Name: {doc['name']} | 📊 Size: {doc['size']} bytes | 🎯 Type: {doc['mime_type']}")
            return documents
        else:
            print(f"❌ Get documents failed: {response.status_code} - {response.text}")
            return []
    except Exception as e:
        print(f"❌ Get documents error: {e}")
        return []

def cleanup_test_files(image_paths):
    """Clean up test files."""
    for path in image_paths:
        try:
            if os.path.exists(path):
                os.unlink(path)
                print(f"🗑️ Cleaned up: {path}")
        except Exception as e:
            print(f"⚠️ Could not clean up {path}: {e}")

def run_comprehensive_test():
    """Run comprehensive API testing."""
    print("🚀 Starting Comprehensive API Testing")
    print("=" * 50)
    
    # Test 1: Check server status
    print("\n📡 Test 1: Server Status Check")
    if not test_server_status():
        print("❌ Server is not running. Please start the server first.")
        return False
    
    # Test 2: Create test images
    print("\n🖼️ Test 2: Creating Test Images")
    test_images = create_test_images(3)
    image_paths = [img['path'] for img in test_images]
    
    try:
        # Test 3: Single upload
        print("\n📁 Test 3: Single Image Upload")
        single_result = test_single_upload(test_images[0]['path'], test_images[0]['name'])
        
        # Test 4: Multiple upload with PDF generation
        print("\n📚 Test 4: Multiple Image Upload with PDF Generation")
        multiple_result = test_multiple_upload_with_pdf(image_paths, "API_Test_Combined_PDF")
        
        # Test 5: Get all documents
        print("\n📋 Test 5: List All Documents")
        documents = test_get_documents()
        
        # Test 6: Convert existing images to PDF
        if documents:
            print("\n🖼️➡️📄 Test 6: Convert Existing Images to PDF")
            # Get image document IDs
            image_docs = [doc for doc in documents if doc['mime_type'].startswith('image/')]
            if len(image_docs) >= 2:
                doc_ids = [doc['id'] for doc in image_docs[:3]]  # Take first 3 images
                conversion_result = test_images_to_pdf_conversion(doc_ids, "API_Manual_Conversion_Test")
            else:
                print("⚠️ Not enough image documents for conversion test")
        
        # Test 7: Final document list
        print("\n📋 Test 7: Final Document List")
        final_documents = test_get_documents()
        
        # Summary
        print("\n" + "=" * 50)
        print("🎉 API Testing Complete!")
        print("=" * 50)
        
        print(f"✅ Total documents created: {len(final_documents)}")
        
        # Count different types
        images = [doc for doc in final_documents if doc['mime_type'].startswith('image/')]
        pdfs = [doc for doc in final_documents if doc['mime_type'] == 'application/pdf']
        
        print(f"🖼️ Image documents: {len(images)}")
        print(f"📄 PDF documents: {len(pdfs)}")
        
        if len(pdfs) > 0:
            print("\n🎯 PDF Generation Tests: ✅ PASSED")
            print("   • Multiple image upload with PDF generation: ✅")
            print("   • Manual image to PDF conversion: ✅")
        else:
            print("\n❌ PDF Generation Tests: FAILED")
        
        return True
        
    finally:
        # Cleanup
        print("\n🧹 Cleaning up test files...")
        cleanup_test_files(image_paths)

if __name__ == "__main__":
    success = run_comprehensive_test()
    if success:
        print("\n🏆 All tests completed successfully!")
        print("🎉 Multiple image upload and PDF generation functionality is working!")
    else:
        print("\n❌ Some tests failed.")
