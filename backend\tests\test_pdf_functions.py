import pytest
import os
import tempfile
from PIL import Image
import sys
import io

# Add the app directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

# Import only the functions we want to test
from app.services.pdf_service import get_image_dimensions, calculate_image_position


class TestPdfFunctions:
    """Test individual PDF service functions without dependencies."""
    
    def test_get_image_dimensions(self):
        """Test getting image dimensions."""
        # Create a test image
        with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as tmp_file:
            try:
                img = Image.new('RGB', (800, 600), color='red')
                img.save(tmp_file.name)
                
                width, height = get_image_dimensions(tmp_file.name)
                assert width == 800
                assert height == 600
            finally:
                # Clean up
                if os.path.exists(tmp_file.name):
                    os.unlink(tmp_file.name)
    
    def test_get_image_dimensions_different_sizes(self):
        """Test getting dimensions for different image sizes."""
        test_cases = [
            (100, 100),
            (1920, 1080),
            (300, 400),
            (50, 200)
        ]
        
        for expected_width, expected_height in test_cases:
            with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as tmp_file:
                try:
                    img = Image.new('RGB', (expected_width, expected_height), color='blue')
                    img.save(tmp_file.name)
                    
                    width, height = get_image_dimensions(tmp_file.name)
                    assert width == expected_width
                    assert height == expected_height
                finally:
                    if os.path.exists(tmp_file.name):
                        os.unlink(tmp_file.name)
    
    def test_calculate_image_position_contain_smaller_image(self):
        """Test image position calculation with contain mode for smaller image."""
        # Image smaller than page - should maintain original size and be centered
        x, y, width, height = calculate_image_position(400, 300, 612, 792, "contain")
        
        # Should maintain aspect ratio
        assert width == 400
        assert height == 300
        # Should be centered with margins
        assert x > 50  # Should have margin
        assert y > 50  # Should have margin
    
    def test_calculate_image_position_contain_larger_image(self):
        """Test image position calculation with contain mode for larger image."""
        # Image larger than page - should be scaled down
        x, y, width, height = calculate_image_position(1000, 800, 612, 792, "contain")
        
        # Should be scaled down to fit
        assert width < 1000
        assert height < 800
        # Should maintain aspect ratio
        aspect_ratio_original = 1000 / 800
        aspect_ratio_scaled = width / height
        assert abs(aspect_ratio_original - aspect_ratio_scaled) < 0.01
    
    def test_calculate_image_position_fill(self):
        """Test image position calculation with fill mode."""
        x, y, width, height = calculate_image_position(400, 300, 612, 792, "fill")
        
        # Should fill available space exactly
        assert x == 50  # Margin
        assert y == 50  # Margin
        assert width == 512  # Page width - 2*margin (612 - 2*50)
        assert height == 692  # Page height - 2*margin (792 - 2*50)
    
    def test_calculate_image_position_cover(self):
        """Test image position calculation with cover mode."""
        x, y, width, height = calculate_image_position(400, 300, 612, 792, "cover")
        
        # Should scale to cover entire page while maintaining aspect ratio
        available_width = 512  # 612 - 2*50
        available_height = 692  # 792 - 2*50
        
        # At least one dimension should fill the available space
        assert width >= available_width or height >= available_height
        
        # Should maintain aspect ratio
        aspect_ratio_original = 400 / 300
        aspect_ratio_scaled = width / height
        assert abs(aspect_ratio_original - aspect_ratio_scaled) < 0.01
    
    def test_calculate_image_position_different_page_sizes(self):
        """Test image position calculation with different page sizes."""
        # Test with A4 size (595, 842)
        x, y, width, height = calculate_image_position(400, 300, 595, 842, "contain")
        assert x >= 50
        assert y >= 50
        assert width <= 495  # 595 - 2*50
        assert height <= 742  # 842 - 2*50
        
        # Test with letter size (612, 792)
        x, y, width, height = calculate_image_position(400, 300, 612, 792, "contain")
        assert x >= 50
        assert y >= 50
        assert width <= 512  # 612 - 2*50
        assert height <= 692  # 792 - 2*50
    
    def test_calculate_image_position_edge_cases(self):
        """Test edge cases for image position calculation."""
        # Very small image
        x, y, width, height = calculate_image_position(10, 10, 612, 792, "contain")
        assert width == 10
        assert height == 10
        
        # Very wide image
        x, y, width, height = calculate_image_position(2000, 100, 612, 792, "contain")
        assert width < 2000  # Should be scaled down
        assert height < 100   # Should be scaled down proportionally
        
        # Very tall image
        x, y, width, height = calculate_image_position(100, 2000, 612, 792, "contain")
        assert width < 100    # Should be scaled down proportionally
        assert height < 2000  # Should be scaled down
    
    def test_calculate_image_position_square_images(self):
        """Test position calculation for square images."""
        # Square image with contain
        x, y, width, height = calculate_image_position(500, 500, 612, 792, "contain")
        assert width == height  # Should remain square
        
        # Square image with cover
        x, y, width, height = calculate_image_position(500, 500, 612, 792, "cover")
        assert width == height  # Should remain square
    
    def test_image_position_centering(self):
        """Test that images are properly centered."""
        # Small image should be centered
        x, y, width, height = calculate_image_position(200, 150, 612, 792, "contain")
        
        available_width = 512  # 612 - 2*50
        available_height = 692  # 792 - 2*50
        
        # Calculate expected centering
        expected_x = 50 + (available_width - width) / 2
        expected_y = 50 + (available_height - height) / 2
        
        assert abs(x - expected_x) < 0.01
        assert abs(y - expected_y) < 0.01


def test_basic_functionality():
    """Simple test to verify basic functionality works."""
    # Test that we can create an image and get its dimensions
    with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as tmp_file:
        try:
            img = Image.new('RGB', (100, 100), color='green')
            img.save(tmp_file.name)
            
            # Verify file exists and has content
            assert os.path.exists(tmp_file.name)
            assert os.path.getsize(tmp_file.name) > 0
            
            # Test our function
            width, height = get_image_dimensions(tmp_file.name)
            assert width == 100
            assert height == 100
            
        finally:
            if os.path.exists(tmp_file.name):
                os.unlink(tmp_file.name)


if __name__ == "__main__":
    # Run a simple test
    test_basic_functionality()
    print("✅ Basic functionality test passed!")
    
    # Run the test class
    test_instance = TestPdfFunctions()
    test_instance.test_get_image_dimensions()
    print("✅ Image dimensions test passed!")
    
    test_instance.test_calculate_image_position_contain_smaller_image()
    print("✅ Image position calculation test passed!")
    
    print("🎉 All manual tests passed!")
