from typing import Optional
from datetime import datetime
from pydantic import BaseModel


class DocumentBase(BaseModel):
    name: str
    mime_type: str


class DocumentCreate(DocumentBase):
    pass


class DocumentResponse(DocumentBase):
    id: int
    file_path: str
    status: str
    user_id: int
    processed_text: Optional[str] = None
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True  # This allows Pydantic to work with SQLAlchemy models


class DocumentListResponse(BaseModel):
    documents: list[DocumentResponse]
    total: int
    page: int
    per_page: int


class DocumentUploadResponse(BaseModel):
    id: int
    name: str
    status: str
    message: str


class BatchUploadRequest(BaseModel):
    generate_pdf: bool = False
    pdf_name: Optional[str] = None


class BatchUploadResponse(BaseModel):
    uploaded_files: list[DocumentUploadResponse]
    batch_id: Optional[str] = None
    pdf_generation_status: Optional[str] = None
    message: str


class ImageToPdfRequest(BaseModel):
    document_ids: list[int]
    pdf_name: str
    page_orientation: str = "portrait"  # portrait or landscape
    image_fit: str = "contain"  # contain, cover, fill


class ImageToPdfResponse(BaseModel):
    pdf_document_id: int
    pdf_name: str
    status: str
    message: str
