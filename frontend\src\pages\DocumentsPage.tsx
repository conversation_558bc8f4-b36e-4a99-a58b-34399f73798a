import React, { useState } from 'react';
import { Header } from 'components/common/Header';
import { Sidebar } from 'components/common/Sidebar';
import { DocumentList } from 'components/documents/DocumentList';
import { BatchUpload } from 'components/documents/BatchUpload';
import { ImagesToPdfConverter } from 'components/documents/ImagesToPdfConverter';
import { useDocuments } from 'hooks/useDocuments';

export const DocumentsPage: React.FC = () => {
  const [activeTab, setActiveTab] = useState<'upload' | 'convert'>('upload');
  const { fetchDocuments } = useDocuments();

  const handleRefreshDocuments = () => {
    fetchDocuments();
  };

  return (
    <div className="min-h-screen bg-gray-100">
      <Header title="Documents" />
      <div className="flex">
        <Sidebar />
        <main className="flex-1 p-6">
          {/* Tab Navigation */}
          <div className="mb-6">
            <div className="border-b border-gray-200">
              <nav className="-mb-px flex space-x-8">
                <button
                  onClick={() => setActiveTab('upload')}
                  className={`py-2 px-1 border-b-2 font-medium text-sm ${
                    activeTab === 'upload'
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  Upload Documents
                </button>
                <button
                  onClick={() => setActiveTab('convert')}
                  className={`py-2 px-1 border-b-2 font-medium text-sm ${
                    activeTab === 'convert'
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  Convert Images to PDF
                </button>
              </nav>
            </div>
          </div>

          {/* Tab Content */}
          <div className="mb-6">
            {activeTab === 'upload' && (
              <BatchUpload onUploadComplete={handleRefreshDocuments} />
            )}
            {activeTab === 'convert' && (
              <ImagesToPdfConverter onConversionComplete={handleRefreshDocuments} />
            )}
          </div>

          {/* Document List */}
          <div className="bg-white rounded-lg shadow">
            <DocumentList />
          </div>
        </main>
      </div>
    </div>
  );
};
