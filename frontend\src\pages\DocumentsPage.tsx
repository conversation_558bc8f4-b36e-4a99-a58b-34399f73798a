import React, { useState } from 'react';
import { Header } from 'components/common/Header';
import { Sidebar } from 'components/common/Sidebar';
import { DocumentList } from 'components/documents/DocumentList';
import { BatchUpload } from 'components/documents/BatchUpload';
import { ImagesToPdfConverter } from 'components/documents/ImagesToPdfConverter';
import { useDocuments } from 'hooks/useDocuments';

export const DocumentsPage: React.FC = () => {
  const [activeTab, setActiveTab] = useState<'upload' | 'convert'>('upload');
  const { fetchDocuments } = useDocuments();

  const handleRefreshDocuments = () => {
    fetchDocuments();
  };

  return (
    <div className="min-h-screen bg-gray-100">
      <Header title="Documents" />
      <div className="flex">
        <Sidebar />
        <main className="flex-1 p-6">
          {/* Page Header */}
          <div className="mb-6">
            <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white p-6 rounded-lg shadow-lg">
              <h1 className="text-2xl font-bold mb-2">
                📚 Document Processing Platform
              </h1>
              <p className="text-blue-100">
                Upload multiple images at once and automatically generate PDF documents
              </p>
            </div>
          </div>

          {/* Tab Navigation */}
          <div className="mb-6">
            <div className="border-b border-gray-200">
              <nav className="-mb-px flex space-x-8">
                <button
                  onClick={() => setActiveTab('upload')}
                  className={`py-3 px-4 border-b-2 font-medium text-sm flex items-center space-x-2 ${
                    activeTab === 'upload'
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  <span>📁</span>
                  <span>Multiple Image Upload & PDF Generation</span>
                  {activeTab === 'upload' && <span className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">Active</span>}
                </button>
                <button
                  onClick={() => setActiveTab('convert')}
                  className={`py-3 px-4 border-b-2 font-medium text-sm flex items-center space-x-2 ${
                    activeTab === 'convert'
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  <span>🖼️➡️📄</span>
                  <span>Convert Existing Images to PDF</span>
                  {activeTab === 'convert' && <span className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">Active</span>}
                </button>
              </nav>
            </div>
          </div>

          {/* Tab Content */}
          <div className="mb-6">
            {activeTab === 'upload' && (
              <BatchUpload onUploadComplete={handleRefreshDocuments} />
            )}
            {activeTab === 'convert' && (
              <ImagesToPdfConverter onConversionComplete={handleRefreshDocuments} />
            )}
          </div>

          {/* Document List */}
          <div className="bg-white rounded-lg shadow">
            <DocumentList />
          </div>
        </main>
      </div>
    </div>
  );
};
