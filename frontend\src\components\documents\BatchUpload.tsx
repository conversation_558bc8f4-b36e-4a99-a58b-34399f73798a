import React, { useState } from 'react';
import { useDocuments } from 'hooks/useDocuments';
import { UploadZone } from './UploadZone';

interface BatchUploadProps {
  onUploadComplete?: () => void;
}

export const BatchUpload: React.FC<BatchUploadProps> = ({ onUploadComplete }) => {
  const { uploadMultipleDocuments } = useDocuments();
  const [uploading, setUploading] = useState(false);
  const [uploadStatus, setUploadStatus] = useState<string | null>(null);

  const handleMultipleUpload = async (
    files: File[], 
    generatePdf?: boolean, 
    pdfName?: string
  ) => {
    setUploading(true);
    setUploadStatus(null);

    try {
      const result = await uploadMultipleDocuments(files, generatePdf, pdfName);
      
      if (generatePdf && result.pdf_generation_status) {
        setUploadStatus(
          `Uploaded ${files.length} files. PDF generation: ${result.pdf_generation_status}`
        );
      } else {
        setUploadStatus(`Successfully uploaded ${files.length} files`);
      }

      if (onUploadComplete) {
        onUploadComplete();
      }
    } catch (error) {
      setUploadStatus(`Upload failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setUploading(false);
    }
  };

  return (
    <div className="space-y-4">
      <div className="bg-white p-6 rounded-lg shadow-sm border">
        <h2 className="text-lg font-semibold mb-4">Upload Documents</h2>
        
        <UploadZone onMultipleUpload={handleMultipleUpload} />
        
        {uploading && (
          <div className="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-md">
            <div className="flex items-center">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 mr-2"></div>
              <span className="text-blue-700">Uploading files...</span>
            </div>
          </div>
        )}
        
        {uploadStatus && !uploading && (
          <div className={`mt-4 p-4 rounded-md ${
            uploadStatus.includes('failed') || uploadStatus.includes('error')
              ? 'bg-red-50 border border-red-200 text-red-700'
              : 'bg-green-50 border border-green-200 text-green-700'
          }`}>
            {uploadStatus}
          </div>
        )}
      </div>

      <div className="bg-gray-50 p-4 rounded-lg">
        <h3 className="font-medium text-gray-900 mb-2">Features:</h3>
        <ul className="text-sm text-gray-600 space-y-1">
          <li>• Upload multiple files at once</li>
          <li>• Automatically combine multiple images into a single PDF</li>
          <li>• Support for folders (select multiple files from folder)</li>
          <li>• Individual file processing and OCR</li>
          <li>• Batch operations for efficiency</li>
        </ul>
      </div>
    </div>
  );
};
