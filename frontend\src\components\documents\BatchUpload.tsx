import React, { useState } from 'react';
import { useDocuments } from 'hooks/useDocuments';
import { UploadZone } from './UploadZone';

interface BatchUploadProps {
  onUploadComplete?: () => void;
}

export const BatchUpload: React.FC<BatchUploadProps> = ({ onUploadComplete }) => {
  const { uploadMultipleDocuments } = useDocuments();
  const [uploading, setUploading] = useState(false);
  const [uploadStatus, setUploadStatus] = useState<string | null>(null);

  const handleMultipleUpload = async (
    files: File[], 
    generatePdf?: boolean, 
    pdfName?: string
  ) => {
    setUploading(true);
    setUploadStatus(null);

    try {
      const result = await uploadMultipleDocuments(files, generatePdf, pdfName);
      
      if (generatePdf && result.pdf_generation_status) {
        setUploadStatus(
          `Uploaded ${files.length} files. PDF generation: ${result.pdf_generation_status}`
        );
      } else {
        setUploadStatus(`Successfully uploaded ${files.length} files`);
      }

      if (onUploadComplete) {
        onUploadComplete();
      }
    } catch (error) {
      setUploadStatus(`Upload failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setUploading(false);
    }
  };

  return (
    <div className="space-y-4">
      <div className="bg-white p-6 rounded-lg shadow-sm border">
        <div className="mb-6">
          <h2 className="text-xl font-bold text-gray-900 mb-2">
            📚 Multiple Image Upload & PDF Generation
          </h2>
          <p className="text-gray-600">
            Select multiple images at once and automatically generate a combined PDF document
          </p>
        </div>

        <UploadZone onMultipleUpload={handleMultipleUpload} />
        
        {uploading && (
          <div className="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-md">
            <div className="flex items-center">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 mr-2"></div>
              <span className="text-blue-700">Uploading files...</span>
            </div>
          </div>
        )}
        
        {uploadStatus && !uploading && (
          <div className={`mt-4 p-4 rounded-md ${
            uploadStatus.includes('failed') || uploadStatus.includes('error')
              ? 'bg-red-50 border border-red-200 text-red-700'
              : 'bg-green-50 border border-green-200 text-green-700'
          }`}>
            {uploadStatus}
          </div>
        )}
      </div>

      <div className="bg-gradient-to-r from-blue-50 to-green-50 p-6 rounded-lg border border-blue-200">
        <h3 className="font-bold text-gray-900 mb-3 flex items-center">
          <span className="mr-2">🎯</span>
          Key Features - Multiple File Selection
        </h3>
        <div className="grid md:grid-cols-2 gap-4">
          <div>
            <h4 className="font-medium text-blue-900 mb-2">📁 File Selection</h4>
            <ul className="text-sm text-gray-700 space-y-1">
              <li>✅ Select multiple images at once</li>
              <li>✅ Drag & drop multiple files</li>
              <li>✅ Ctrl/Cmd + click for multi-select</li>
              <li>✅ Support for entire folders</li>
            </ul>
          </div>
          <div>
            <h4 className="font-medium text-green-900 mb-2">📄 PDF Generation</h4>
            <ul className="text-sm text-gray-700 space-y-1">
              <li>✅ Automatic PDF combination</li>
              <li>✅ Custom PDF naming</li>
              <li>✅ Professional page layout</li>
              <li>✅ Batch processing efficiency</li>
            </ul>
          </div>
        </div>
        <div className="mt-4 p-3 bg-white rounded-md border border-blue-300">
          <p className="text-sm text-blue-800 font-medium">
            💡 <strong>How to select multiple files:</strong> Click "Choose Files" → Hold Ctrl (Windows) or Cmd (Mac) → Click multiple images → Open
          </p>
        </div>
      </div>
    </div>
  );
};
