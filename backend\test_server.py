"""
Simple test server for manual testing of the Document Processing Platform
Multiple Image Upload and PDF Generation functionality.
"""

from fastapi import FastAPI, UploadFile, File, Form, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from typing import List, Optional
import os
import uuid
from PIL import Image
from reportlab.pdfgen import canvas
from reportlab.lib.pagesizes import letter, landscape

app = FastAPI(
    title="Document Processing Platform - Test Server",
    description="""
    ## 🚀 Multiple Image Upload & PDF Generation Test Server

    This server provides endpoints to test:
    - **Multiple image upload** with automatic PDF generation
    - **Convert existing images** to PDF with different configurations
    - **Single image upload** for basic functionality
    - **Document management** (list, view, delete)

    ## 🧪 Key Testing Endpoints:
    - **POST /upload-multiple** - Upload multiple images, optionally generate PDF
    - **POST /images-to-pdf** - Convert existing images to PDF
    - **POST /upload** - Upload single file
    - **GET /documents** - List all uploaded documents
    """,
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Create uploads directory if it doesn't exist
os.makedirs("uploads", exist_ok=True)

# Simple in-memory storage for testing
documents = []
document_counter = 1

def get_image_dimensions(image_path: str) -> tuple[int, int]:
    """Get image dimensions."""
    with Image.open(image_path) as img:
        return img.size

def calculate_image_position(img_width: int, img_height: int, page_width: float, page_height: float, 
                           fit_mode: str = "contain") -> tuple[float, float, float, float]:
    """Calculate image position and size to fit on page."""
    margin = 50
    available_width = page_width - 2 * margin
    available_height = page_height - 2 * margin
    
    if fit_mode == "contain":
        scale_x = available_width / img_width
        scale_y = available_height / img_height
        scale = min(scale_x, scale_y)
        
        new_width = img_width * scale
        new_height = img_height * scale
        
        x = margin + (available_width - new_width) / 2
        y = margin + (available_height - new_height) / 2
        
        return x, y, new_width, new_height
    
    elif fit_mode == "fill":
        return margin, margin, available_width, available_height
    
    else:  # cover
        scale_x = available_width / img_width
        scale_y = available_height / img_height
        scale = max(scale_x, scale_y)
        
        new_width = img_width * scale
        new_height = img_height * scale
        
        x = margin + (available_width - new_width) / 2
        y = margin + (available_height - new_height) / 2
        
        return x, y, new_width, new_height

@app.get("/")
async def root():
    return {
        "message": "Document Processing Platform Test Server",
        "status": "running",
        "endpoints": {
            "upload_single": "/upload",
            "upload_multiple": "/upload-multiple", 
            "images_to_pdf": "/images-to-pdf",
            "documents": "/documents",
            "docs": "/docs"
        }
    }

@app.post("/upload",
          summary="📁 Upload Single Document",
          description="Upload a single image or PDF file. Supports PNG, JPG, JPEG, PDF formats up to 10MB.")
async def upload_single_document(file: UploadFile = File(...)):
    """Upload a single document."""
    global document_counter
    
    # Validate file type
    allowed_types = ['image/png', 'image/jpeg', 'image/jpg', 'application/pdf']
    if file.content_type not in allowed_types:
        raise HTTPException(status_code=400, detail=f"File type {file.content_type} not allowed")
    
    # Save file
    filename = file.filename or "unknown"
    file_extension = os.path.splitext(filename)[1]
    unique_filename = f"{uuid.uuid4()}{file_extension}"
    file_path = os.path.join("uploads", unique_filename)
    
    with open(file_path, "wb") as buffer:
        content = await file.read()
        buffer.write(content)
    
    # Create document record
    document = {
        "id": document_counter,
        "name": file.filename,
        "file_path": file_path,
        "mime_type": file.content_type,
        "status": "completed",
        "size": len(content)
    }
    
    documents.append(document)
    document_counter += 1
    
    return {
        "id": document["id"],
        "name": document["name"],
        "status": "completed",
        "message": "Document uploaded successfully"
    }

@app.post("/upload-multiple",
          summary="📚 Upload Multiple Images & Generate PDF",
          description="""
          🎯 **KEY FEATURE**: Upload multiple images at once and optionally generate a combined PDF.

          **Parameters:**
          - `files`: Multiple image files (PNG, JPG, JPEG)
          - `generate_pdf`: Set to `true` to automatically create PDF from uploaded images
          - `pdf_name`: Name for the generated PDF (required if generate_pdf=true)

          **Example Usage:**
          1. Select multiple image files
          2. Set generate_pdf=true
          3. Set pdf_name="My_Combined_Images"
          4. Execute to upload images and create PDF automatically
          """)
async def upload_multiple_documents(
    files: List[UploadFile] = File(...),
    generate_pdf: bool = Form(False),
    pdf_name: Optional[str] = Form(None)
):
    """Upload multiple documents and optionally generate PDF."""
    global document_counter
    
    uploaded_files = []
    uploaded_document_ids = []
    
    # Upload all files
    for file in files:
        try:
            # Validate file type
            allowed_types = ['image/png', 'image/jpeg', 'image/jpg', 'application/pdf']
            if file.content_type not in allowed_types:
                uploaded_files.append({
                    "id": 0,
                    "name": file.filename,
                    "status": "failed",
                    "message": f"File type {file.content_type} not allowed"
                })
                continue
            
            # Save file
            filename = file.filename or "unknown"
            file_extension = os.path.splitext(filename)[1]
            unique_filename = f"{uuid.uuid4()}{file_extension}"
            file_path = os.path.join("uploads", unique_filename)
            
            with open(file_path, "wb") as buffer:
                content = await file.read()
                buffer.write(content)
            
            # Create document record
            document = {
                "id": document_counter,
                "name": file.filename,
                "file_path": file_path,
                "mime_type": file.content_type,
                "status": "completed",
                "size": len(content)
            }
            
            documents.append(document)
            uploaded_document_ids.append(document_counter)
            
            uploaded_files.append({
                "id": document_counter,
                "name": file.filename,
                "status": "completed",
                "message": "Document uploaded successfully"
            })
            
            document_counter += 1
            
        except Exception as e:
            uploaded_files.append({
                "id": 0,
                "name": file.filename,
                "status": "failed",
                "message": f"Upload failed: {str(e)}"
            })
    
    # Generate PDF if requested
    pdf_generation_status = None
    batch_id = None
    
    if generate_pdf and uploaded_document_ids and pdf_name:
        try:
            # Filter image documents
            image_docs = [doc for doc in documents if doc["id"] in uploaded_document_ids and doc["mime_type"].startswith("image/")]
            
            if image_docs:
                # Generate PDF
                pdf_filename = f"{uuid.uuid4()}_{pdf_name}.pdf"
                pdf_path = os.path.join("uploads", pdf_filename)
                
                c = canvas.Canvas(pdf_path, pagesize=letter)
                page_width, page_height = letter
                
                for doc in image_docs:
                    try:
                        img_width, img_height = get_image_dimensions(doc["file_path"])
                        x, y, width, height = calculate_image_position(
                            img_width, img_height, page_width, page_height, "contain"
                        )
                        c.drawImage(doc["file_path"], x, y, width=width, height=height)
                        c.showPage()
                    except Exception as e:
                        print(f"Error processing image {doc['name']}: {str(e)}")
                        continue
                
                c.save()
                
                # Create PDF document record
                pdf_document = {
                    "id": document_counter,
                    "name": f"{pdf_name}.pdf",
                    "file_path": pdf_path,
                    "mime_type": "application/pdf",
                    "status": "completed",
                    "size": os.path.getsize(pdf_path)
                }
                
                documents.append(pdf_document)
                document_counter += 1
                
                pdf_generation_status = "completed"
                batch_id = f"batch_{uuid.uuid4()}"
            else:
                pdf_generation_status = "failed: No image files found"
                
        except Exception as e:
            pdf_generation_status = f"failed: {str(e)}"
    
    return {
        "uploaded_files": uploaded_files,
        "batch_id": batch_id,
        "pdf_generation_status": pdf_generation_status,
        "message": f"Uploaded {len(uploaded_files)} files successfully"
    }

@app.post("/images-to-pdf",
          summary="🖼️➡️📄 Convert Existing Images to PDF",
          description="""
          🎯 **KEY FEATURE**: Convert previously uploaded images into a single PDF with custom configuration.

          **Request Body Example:**
          ```json
          {
            "document_ids": [1, 2, 3],
            "pdf_name": "My_Converted_PDF",
            "page_orientation": "portrait",
            "image_fit": "contain"
          }
          ```

          **Parameters:**
          - `document_ids`: Array of document IDs to convert (get IDs from /documents endpoint)
          - `pdf_name`: Name for the generated PDF
          - `page_orientation`: "portrait" or "landscape"
          - `image_fit`: "contain" (fit within page), "cover" (fill page), or "fill" (stretch)

          **Steps to Test:**
          1. First upload images using /upload or /upload-multiple
          2. Get document IDs from /documents endpoint
          3. Use those IDs in this endpoint to create PDF
          """)
async def convert_images_to_pdf(request_data: dict):
    """Convert multiple images to a single PDF."""
    global document_counter
    
    document_ids = request_data.get("document_ids", [])
    pdf_name = request_data.get("pdf_name", "converted_images")
    page_orientation = request_data.get("page_orientation", "portrait")
    image_fit = request_data.get("image_fit", "contain")
    
    if not document_ids:
        raise HTTPException(status_code=400, detail="No document IDs provided")
    
    if not pdf_name.strip():
        raise HTTPException(status_code=400, detail="PDF name is required")
    
    # Get documents
    selected_docs = [doc for doc in documents if doc["id"] in document_ids]
    
    if not selected_docs:
        raise HTTPException(status_code=404, detail="No documents found")
    
    # Filter image documents
    image_docs = [doc for doc in selected_docs if doc["mime_type"].startswith("image/")]
    
    if not image_docs:
        raise HTTPException(status_code=400, detail="No image documents found")
    
    try:
        # Set page size
        if page_orientation == "landscape":
            pagesize = landscape(letter)
        else:
            pagesize = letter
        
        # Generate PDF
        pdf_filename = f"{uuid.uuid4()}_{pdf_name}.pdf"
        pdf_path = os.path.join("uploads", pdf_filename)
        
        c = canvas.Canvas(pdf_path, pagesize=pagesize)
        page_width, page_height = pagesize
        
        for doc in image_docs:
            try:
                img_width, img_height = get_image_dimensions(doc["file_path"])
                x, y, width, height = calculate_image_position(
                    img_width, img_height, page_width, page_height, image_fit
                )
                c.drawImage(doc["file_path"], x, y, width=width, height=height)
                c.showPage()
            except Exception as e:
                print(f"Error processing image {doc['name']}: {str(e)}")
                continue
        
        c.save()
        
        # Create PDF document record
        pdf_document = {
            "id": document_counter,
            "name": f"{pdf_name}.pdf",
            "file_path": pdf_path,
            "mime_type": "application/pdf",
            "status": "completed",
            "size": os.path.getsize(pdf_path)
        }
        
        documents.append(pdf_document)
        document_counter += 1
        
        return {
            "pdf_document_id": pdf_document["id"],
            "pdf_name": pdf_document["name"],
            "status": "completed",
            "message": f"PDF generated successfully with {len(image_docs)} images"
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"PDF generation failed: {str(e)}")

@app.get("/documents")
async def get_documents():
    """Get all documents."""
    return {"documents": documents}

@app.get("/documents/{document_id}")
async def get_document(document_id: int):
    """Get a specific document."""
    document = next((doc for doc in documents if doc["id"] == document_id), None)
    if not document:
        raise HTTPException(status_code=404, detail="Document not found")
    return document

@app.delete("/documents/{document_id}")
async def delete_document(document_id: int):
    """Delete a document."""
    global documents
    document = next((doc for doc in documents if doc["id"] == document_id), None)
    if not document:
        raise HTTPException(status_code=404, detail="Document not found")
    
    # Remove file
    try:
        if os.path.exists(document["file_path"]):
            os.unlink(document["file_path"])
    except Exception as e:
        print(f"Error deleting file: {e}")
    
    # Remove from documents list
    documents = [doc for doc in documents if doc["id"] != document_id]
    
    return {"message": "Document deleted successfully"}

if __name__ == "__main__":
    import uvicorn
    print("🚀 Starting Document Processing Platform Test Server...")
    print("📋 Available endpoints:")
    print("   • http://localhost:8000/docs - API Documentation")
    print("   • http://localhost:8000/upload - Single file upload")
    print("   • http://localhost:8000/upload-multiple - Multiple file upload")
    print("   • http://localhost:8000/images-to-pdf - Convert images to PDF")
    print("   • http://localhost:8000/documents - List all documents")
    print("\n🧪 Ready for manual testing!")
    
    uvicorn.run(app, host="0.0.0.0", port=8000)
