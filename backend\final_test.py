"""
Final comprehensive test of the PDF generation functionality
"""

import requests
from PIL import Image
import tempfile
import os
import time

def create_test_image(color='red', size=(300, 300), name_suffix=''):
    """Create a test image."""
    tmp_file = tempfile.NamedTemporaryFile(suffix=f'_test{name_suffix}.png', delete=False)
    tmp_file.close()
    
    img = Image.new('RGB', size, color=color)
    img.save(tmp_file.name)
    
    print(f"✅ Created {color} test image: {os.path.basename(tmp_file.name)} ({size[0]}x{size[1]})")
    return tmp_file.name

def test_pdf_functionality():
    """Test the complete PDF generation functionality."""
    base_url = "http://localhost:8000"
    
    print("🚀 COMPREHENSIVE PDF GENERATION TEST")
    print("=" * 50)
    
    # Test 1: Server status
    print("\n📡 Test 1: Server Status")
    try:
        response = requests.get(f"{base_url}/")
        print(f"✅ Server: {response.status_code} - {response.json()}")
    except Exception as e:
        print(f"❌ Server not responding: {e}")
        return False
    
    # Test 2: Test endpoint
    print("\n🧪 Test 2: Test Endpoint")
    try:
        response = requests.get(f"{base_url}/test")
        print(f"✅ Test endpoint: {response.status_code} - {response.json()}")
    except Exception as e:
        print(f"❌ Test endpoint failed: {e}")
    
    # Test 3: Single image upload
    print("\n📁 Test 3: Single Image Upload")
    image1_path = create_test_image('red', (400, 300), '_single')
    
    try:
        with open(image1_path, 'rb') as f:
            files = {'file': ('red_image.png', f, 'image/png')}
            response = requests.post(f"{base_url}/upload", files=files)
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Single upload successful: {result}")
        else:
            print(f"❌ Single upload failed: {response.status_code} - {response.text}")
            
    except Exception as e:
        print(f"❌ Single upload error: {e}")
    finally:
        try:
            os.unlink(image1_path)
        except:
            pass
    
    # Test 4: Multiple image upload with PDF generation
    print("\n📚 Test 4: Multiple Image Upload with PDF Generation")
    
    # Create multiple test images
    colors = ['red', 'green', 'blue']
    sizes = [(300, 300), (400, 350), (350, 400)]
    image_paths = []
    
    for i, (color, size) in enumerate(zip(colors, sizes)):
        path = create_test_image(color, size, f'_multi_{i}')
        image_paths.append(path)
    
    try:
        # Prepare files for upload
        files = []
        for i, img_path in enumerate(image_paths):
            with open(img_path, 'rb') as f:
                files.append(('files', (f'{colors[i]}_image.png', f.read(), 'image/png')))
        
        # Upload with PDF generation
        data = {
            'generate_pdf': 'true',
            'pdf_name': 'Comprehensive_Test_PDF'
        }
        
        response = requests.post(f"{base_url}/upload-multiple", files=files, data=data)
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Multiple upload successful:")
            print(f"   📁 Uploaded files: {result['uploaded_count']}")
            print(f"   📄 PDF status: {result['pdf_status']}")
            print(f"   💬 Message: {result['message']}")
        else:
            print(f"❌ Multiple upload failed: {response.status_code} - {response.text}")
            
    except Exception as e:
        print(f"❌ Multiple upload error: {e}")
    finally:
        # Clean up
        for path in image_paths:
            try:
                os.unlink(path)
            except:
                pass
    
    # Test 5: Check documents
    print("\n📋 Test 5: Document List")
    try:
        response = requests.get(f"{base_url}/documents")
        if response.status_code == 200:
            result = response.json()
            documents = result.get('documents', [])
            
            print(f"✅ Found {len(documents)} documents:")
            
            images = []
            pdfs = []
            
            for doc in documents:
                doc_type = "📄 PDF" if doc['mime_type'] == 'application/pdf' else "🖼️ Image"
                print(f"   {doc_type} ID:{doc['id']} - {doc['name']} ({doc['size']} bytes)")
                
                if doc['mime_type'] == 'application/pdf':
                    pdfs.append(doc)
                else:
                    images.append(doc)
            
            print(f"\n📊 Summary: {len(images)} images, {len(pdfs)} PDFs")
            
            # Test 6: Convert existing images to PDF
            if len(images) >= 2:
                print("\n🖼️➡️📄 Test 6: Convert Existing Images to PDF")
                
                # Use first 2 images
                image_ids = [img['id'] for img in images[:2]]
                
                try:
                    data = {
                        "document_ids": image_ids,
                        "pdf_name": "Manual_Conversion_Test"
                    }
                    
                    response = requests.post(f"{base_url}/images-to-pdf", json=data)
                    
                    if response.status_code == 200:
                        result = response.json()
                        print(f"✅ Manual conversion successful:")
                        print(f"   📄 PDF ID: {result['pdf_id']}")
                        print(f"   📝 PDF Name: {result['pdf_name']}")
                        print(f"   ✅ Status: {result['status']}")
                        print(f"   💬 Message: {result['message']}")
                    else:
                        print(f"❌ Manual conversion failed: {response.status_code} - {response.text}")
                        
                except Exception as e:
                    print(f"❌ Manual conversion error: {e}")
            else:
                print("\n⚠️ Test 6: Skipped (not enough images)")
                
        else:
            print(f"❌ Documents list failed: {response.status_code} - {response.text}")
            
    except Exception as e:
        print(f"❌ Documents list error: {e}")
    
    # Test 7: Final document count
    print("\n📋 Test 7: Final Document Count")
    try:
        response = requests.get(f"{base_url}/documents")
        if response.status_code == 200:
            result = response.json()
            documents = result.get('documents', [])
            
            images = [doc for doc in documents if doc['mime_type'].startswith('image/')]
            pdfs = [doc for doc in documents if doc['mime_type'] == 'application/pdf']
            
            print(f"✅ Final count: {len(documents)} total documents")
            print(f"   🖼️ Images: {len(images)}")
            print(f"   📄 PDFs: {len(pdfs)}")
            
            # Check uploads folder
            uploads_dir = "backend/uploads"
            if os.path.exists(uploads_dir):
                files_in_uploads = os.listdir(uploads_dir)
                pdf_files = [f for f in files_in_uploads if f.endswith('.pdf')]
                print(f"   💾 Files in uploads folder: {len(files_in_uploads)}")
                print(f"   📄 PDF files on disk: {len(pdf_files)}")
                
                if pdf_files:
                    print(f"   📄 Generated PDFs:")
                    for pdf_file in pdf_files:
                        file_path = os.path.join(uploads_dir, pdf_file)
                        file_size = os.path.getsize(file_path)
                        print(f"      • {pdf_file} ({file_size} bytes)")
            
            # Final verdict
            print("\n" + "=" * 50)
            if len(pdfs) > 0:
                print("🎉 SUCCESS! PDF GENERATION IS WORKING!")
                print("✅ Multiple image upload: WORKING")
                print("✅ Automatic PDF generation: WORKING")
                print("✅ Manual image-to-PDF conversion: WORKING")
                print("✅ File storage: WORKING")
                return True
            else:
                print("❌ FAILED! No PDFs were generated")
                return False
                
    except Exception as e:
        print(f"❌ Final count error: {e}")
        return False

if __name__ == "__main__":
    success = test_pdf_functionality()
    
    if success:
        print("\n🏆 ALL TESTS PASSED!")
        print("🎯 The multiple image upload and PDF generation functionality is working correctly!")
    else:
        print("\n❌ SOME TESTS FAILED!")
        print("🔧 Please check the server logs for errors.")
