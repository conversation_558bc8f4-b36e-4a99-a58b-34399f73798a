"""
Manual Testing Server for PDF Generation Functionality
Simplified server for testing single and multiple image PDF conversion
"""

from fastapi import FastAPI, UploadFile, File, Form, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import FileResponse
from typing import List, Optional
import os
import uuid
from PIL import Image
from reportlab.pdfgen import canvas
from reportlab.lib.pagesizes import letter, landscape

# Create FastAPI app
app = FastAPI(
    title="PDF Generation Manual Test Server",
    description="""
    ## 🧪 Manual Testing Server for PDF Generation
    
    This server provides endpoints to manually test:
    - **Single image upload** and PDF conversion
    - **Multiple image upload** with automatic PDF generation
    - **Convert existing images** to PDF with different configurations
    
    ## 🎯 Key Testing Endpoints:
    - **POST /upload** - Upload single image
    - **POST /upload-multiple** - Upload multiple images, optionally generate PDF
    - **POST /images-to-pdf** - Convert existing images to PDF
    - **GET /documents** - List all uploaded documents
    - **GET /download/{filename}** - Download generated PDFs
    """,
    version="1.0.0"
)

# Add CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Create uploads directory
os.makedirs("uploads", exist_ok=True)

# Simple storage
documents = []
doc_id = 1

def get_image_dimensions(image_path: str) -> tuple[int, int]:
    """Get image dimensions."""
    with Image.open(image_path) as img:
        return img.size

def calculate_image_position(img_width: int, img_height: int, page_width: float, page_height: float, 
                           fit_mode: str = "contain") -> tuple[float, float, float, float]:
    """Calculate image position and size to fit on page."""
    margin = 50
    available_width = page_width - 2 * margin
    available_height = page_height - 2 * margin
    
    if fit_mode == "contain":
        scale_x = available_width / img_width
        scale_y = available_height / img_height
        scale = min(scale_x, scale_y)
        
        new_width = img_width * scale
        new_height = img_height * scale
        
        x = margin + (available_width - new_width) / 2
        y = margin + (available_height - new_height) / 2
        
        return x, y, new_width, new_height
    
    elif fit_mode == "fill":
        return margin, margin, available_width, available_height
    
    else:  # cover
        scale_x = available_width / img_width
        scale_y = available_height / img_height
        scale = max(scale_x, scale_y)
        
        new_width = img_width * scale
        new_height = img_height * scale
        
        x = margin + (available_width - new_width) / 2
        y = margin + (available_height - new_height) / 2
        
        return x, y, new_width, new_height

@app.get("/")
def root():
    return {
        "message": "PDF Generation Manual Test Server",
        "status": "running",
        "endpoints": {
            "docs": "/docs",
            "upload_single": "/upload",
            "upload_multiple": "/upload-multiple",
            "images_to_pdf": "/images-to-pdf",
            "documents": "/documents",
            "download": "/download/{filename}"
        },
        "instructions": "Go to /docs for interactive API testing"
    }

@app.post("/upload", 
          summary="📁 Upload Single Image",
          description="Upload a single image file. Supports PNG, JPG, JPEG formats.")
async def upload_single_image(file: UploadFile = File(...)):
    """Upload a single image."""
    global doc_id
    
    # Validate file type
    allowed_types = ['image/png', 'image/jpeg', 'image/jpg']
    if file.content_type not in allowed_types:
        raise HTTPException(status_code=400, detail=f"File type {file.content_type} not allowed. Use PNG or JPG.")
    
    # Save file
    filename = file.filename or "unknown"
    file_extension = os.path.splitext(filename)[1]
    unique_filename = f"{uuid.uuid4()}{file_extension}"
    file_path = os.path.join("uploads", unique_filename)
    
    with open(file_path, "wb") as buffer:
        content = await file.read()
        buffer.write(content)
    
    # Store document info
    doc = {
        "id": doc_id,
        "name": filename,
        "file_path": file_path,
        "mime_type": file.content_type,
        "size": len(content),
        "type": "image"
    }
    documents.append(doc)
    doc_id += 1
    
    return {
        "id": doc["id"], 
        "name": doc["name"], 
        "status": "uploaded",
        "message": f"Image '{filename}' uploaded successfully. Use /images-to-pdf to convert to PDF."
    }

@app.post("/upload-multiple",
          summary="📚 Upload Multiple Images & Generate PDF",
          description="""
          🎯 **KEY FEATURE**: Upload multiple images at once and optionally generate a combined PDF.
          
          **Parameters:**
          - `files`: Multiple image files (PNG, JPG, JPEG)
          - `generate_pdf`: Set to `true` to automatically create PDF from uploaded images
          - `pdf_name`: Name for the generated PDF (required if generate_pdf=true)
          
          **Example Usage:**
          1. Select multiple image files
          2. Set generate_pdf=true
          3. Set pdf_name="My_Combined_Images"
          4. Execute to upload images and create PDF automatically
          """)
async def upload_multiple_images(
    files: List[UploadFile] = File(...),
    generate_pdf: bool = Form(False),
    pdf_name: Optional[str] = Form(None)
):
    """Upload multiple images and optionally generate PDF."""
    global doc_id
    
    uploaded = []
    uploaded_ids = []
    
    # Upload all files
    for file in files:
        try:
            # Validate file type
            allowed_types = ['image/png', 'image/jpeg', 'image/jpg']
            if file.content_type not in allowed_types:
                uploaded.append({
                    "id": 0,
                    "name": file.filename,
                    "status": "failed",
                    "message": f"File type {file.content_type} not allowed"
                })
                continue
            
            filename = file.filename or "unknown"
            file_extension = os.path.splitext(filename)[1]
            unique_filename = f"{uuid.uuid4()}{file_extension}"
            file_path = os.path.join("uploads", unique_filename)
            
            with open(file_path, "wb") as buffer:
                content = await file.read()
                buffer.write(content)
            
            doc = {
                "id": doc_id,
                "name": filename,
                "file_path": file_path,
                "mime_type": file.content_type,
                "size": len(content),
                "type": "image"
            }
            documents.append(doc)
            uploaded_ids.append(doc_id)
            
            uploaded.append({
                "id": doc_id,
                "name": filename,
                "status": "uploaded",
                "message": "Image uploaded successfully"
            })
            
            doc_id += 1
            
        except Exception as e:
            uploaded.append({
                "id": 0,
                "name": file.filename or "unknown",
                "status": "failed",
                "message": f"Upload failed: {str(e)}"
            })
    
    pdf_info = None
    
    # Generate PDF if requested
    if generate_pdf and uploaded_ids and pdf_name:
        try:
            # Get uploaded image documents
            image_docs = [doc for doc in documents if doc["id"] in uploaded_ids]
            
            if image_docs:
                # Create PDF
                pdf_filename = f"{uuid.uuid4()}_{pdf_name}.pdf"
                pdf_path = os.path.join("uploads", pdf_filename)
                
                c = canvas.Canvas(pdf_path, pagesize=letter)
                page_width, page_height = letter
                
                for doc in image_docs:
                    try:
                        img_width, img_height = get_image_dimensions(doc["file_path"])
                        x, y, width, height = calculate_image_position(
                            img_width, img_height, page_width, page_height, "contain"
                        )
                        c.drawImage(doc["file_path"], x, y, width=width, height=height)
                        c.showPage()
                    except Exception as e:
                        print(f"Error processing image: {e}")
                        continue
                
                c.save()
                
                # Add PDF to documents
                pdf_doc = {
                    "id": doc_id,
                    "name": f"{pdf_name}.pdf",
                    "file_path": pdf_path,
                    "mime_type": "application/pdf",
                    "size": os.path.getsize(pdf_path),
                    "type": "pdf"
                }
                documents.append(pdf_doc)
                doc_id += 1
                
                pdf_info = {
                    "pdf_id": pdf_doc["id"],
                    "pdf_name": pdf_doc["name"],
                    "pdf_size": pdf_doc["size"],
                    "status": "generated"
                }
            else:
                pdf_info = {"status": "failed", "message": "No valid images to convert"}
                
        except Exception as e:
            pdf_info = {"status": "failed", "message": str(e)}
    
    return {
        "uploaded_files": uploaded,
        "uploaded_count": len([u for u in uploaded if u["status"] == "uploaded"]),
        "pdf_info": pdf_info,
        "message": f"Processed {len(uploaded)} files"
    }

@app.post("/images-to-pdf",
          summary="🖼️➡️📄 Convert Existing Images to PDF",
          description="""
          🎯 **KEY FEATURE**: Convert previously uploaded images into a single PDF with custom configuration.
          
          **Request Body Example:**
          ```json
          {
            "document_ids": [1, 2, 3],
            "pdf_name": "My_Converted_PDF",
            "page_orientation": "portrait",
            "image_fit": "contain"
          }
          ```
          
          **Parameters:**
          - `document_ids`: Array of document IDs to convert (get IDs from /documents endpoint)
          - `pdf_name`: Name for the generated PDF
          - `page_orientation`: "portrait" or "landscape"
          - `image_fit`: "contain" (fit within page), "cover" (fill page), or "fill" (stretch)
          """)
def convert_images_to_pdf(request: dict):
    """Convert existing images to PDF."""
    global doc_id
    
    document_ids = request.get("document_ids", [])
    pdf_name = request.get("pdf_name", "converted_images")
    page_orientation = request.get("page_orientation", "portrait")
    image_fit = request.get("image_fit", "contain")
    
    if not document_ids:
        raise HTTPException(status_code=400, detail="No document IDs provided")
    
    # Get documents
    selected_docs = [doc for doc in documents if doc["id"] in document_ids and doc["type"] == "image"]
    
    if not selected_docs:
        raise HTTPException(status_code=404, detail="No image documents found with provided IDs")
    
    try:
        # Set page size
        if page_orientation == "landscape":
            pagesize = landscape(letter)
        else:
            pagesize = letter
        
        # Create PDF
        pdf_filename = f"{uuid.uuid4()}_{pdf_name}.pdf"
        pdf_path = os.path.join("uploads", pdf_filename)
        
        c = canvas.Canvas(pdf_path, pagesize=pagesize)
        page_width, page_height = pagesize
        
        for doc in selected_docs:
            try:
                img_width, img_height = get_image_dimensions(doc["file_path"])
                x, y, width, height = calculate_image_position(
                    img_width, img_height, page_width, page_height, image_fit
                )
                c.drawImage(doc["file_path"], x, y, width=width, height=height)
                c.showPage()
            except Exception as e:
                print(f"Error processing image: {e}")
                continue
        
        c.save()
        
        # Add PDF to documents
        pdf_doc = {
            "id": doc_id,
            "name": f"{pdf_name}.pdf",
            "file_path": pdf_path,
            "mime_type": "application/pdf",
            "size": os.path.getsize(pdf_path),
            "type": "pdf"
        }
        documents.append(pdf_doc)
        doc_id += 1
        
        return {
            "pdf_id": pdf_doc["id"],
            "pdf_name": pdf_doc["name"],
            "pdf_size": pdf_doc["size"],
            "pages": len(selected_docs),
            "orientation": page_orientation,
            "image_fit": image_fit,
            "status": "generated",
            "message": f"PDF created from {len(selected_docs)} images",
            "download_url": f"/download/{pdf_doc['name']}"
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"PDF generation failed: {str(e)}")

@app.get("/documents",
         summary="📋 List All Documents",
         description="Get a list of all uploaded images and generated PDFs.")
def get_documents():
    """Get all documents."""
    return {
        "documents": documents,
        "total_count": len(documents),
        "images": len([d for d in documents if d["type"] == "image"]),
        "pdfs": len([d for d in documents if d["type"] == "pdf"])
    }

@app.get("/download/{filename}",
         summary="📥 Download PDF",
         description="Download a generated PDF file.")
def download_file(filename: str):
    """Download a file."""
    file_path = os.path.join("uploads", filename)
    if os.path.exists(file_path):
        return FileResponse(file_path, filename=filename)
    else:
        raise HTTPException(status_code=404, detail="File not found")

if __name__ == "__main__":
    import uvicorn
    print("🚀 Starting PDF Generation Manual Test Server...")
    print("=" * 60)
    print("📋 Available endpoints:")
    print("   • http://localhost:8000/docs - Interactive API Documentation")
    print("   • POST /upload - Upload single image")
    print("   • POST /upload-multiple - Upload multiple images + PDF generation")
    print("   • POST /images-to-pdf - Convert existing images to PDF")
    print("   • GET /documents - List all documents")
    print("   • GET /download/{filename} - Download PDFs")
    print("=" * 60)
    print("🧪 Ready for manual testing!")
    print("📖 Go to http://localhost:8000/docs to start testing")
    
    uvicorn.run(app, host="0.0.0.0", port=8000)
